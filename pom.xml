<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <repositories>
        <repository>
            <id>ruima-nexus</id>
            <name>RuimaNexus</name>
            <url>http://59.172.92.78:8081/repository/maven_ruima</url>
        </repository>
    </repositories>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.4</version>
        <relativePath/>
    </parent>

    <groupId>net.airuima.sygd.plugin</groupId>
    <artifactId>rmes-sygd-plugin</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>rmes-sygd-plugin</name>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <profiles>
        <profile>
            <id>base</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <dependencies>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>rmes</artifactId>
                    <version>1.6.3-SNAPSHOT</version>
                    <scope>provided</scope>
                </dependency>
            </dependencies>
        </profile>
        <profile>
            <id>debug</id>
            <dependencies>
                <dependency>
                    <groupId>net.airuima</groupId>
                    <artifactId>rmes</artifactId>
                    <version>1.6.3-SNAPSHOT</version>
                </dependency>
            </dependencies>
        </profile>



    </profiles>

</project>
