package net.airuima.sygd.piugin.util;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import net.airuima.constant.Constants;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class StringArrayUtils {

    /**
     * 字符串转数组
     *
     * @param text 字符串
     * @return List<String> 数组
     */
    public static List<String> stringToArray(String text) {
        List<String> list = new ArrayList<>();
        if (ObjectUtils.isEmpty(text)) {
            return list;
        }
        if (!text.contains(",")) {
            list.add(text);
            return list;
        }
        List<String> splitStringList = Splitter.on(",")
                .trimResults()
                .splitToList(text);
        if (CollectionUtils.isEmpty(splitStringList)) {
            return list;
        }
        for (String split : splitStringList) {
            if (!ObjectUtils.isEmpty(split)) {
                list.add(split.trim().replaceAll("\\s", ""));
            }
        }
        return list;
    }

    /**
     * 数组转字符串
     *
     * @param array 数组
     * @return String 字符串
     */
    public static String arrayToString(List<String> array) {
        if (ObjectUtils.isEmpty(array)) {
            return null;
        }
        if (array.size() == Constants.INT_ONE) {
            return array.get(Constants.INT_ZERO);
        }
        // 去重
        array = array.stream()
               .distinct()
               .toList();
        return Joiner.on(",")
                .join(array);
    }
}
