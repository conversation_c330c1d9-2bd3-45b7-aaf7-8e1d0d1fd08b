package net.airuima.sygd.piugin.util;

import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class BeanUtilsWithListCopy {

    private static final int MAX_DEPTH = 5; // 最大递归深度

    /**
     * 执行Bean属性复制，同时处理List类型的属性，支持嵌套的List（最多5层）
     * @param source 源对象
     * @param target 目标对象
     */
    public static void copyPropertiesWithList(Object source, Object target) {
        copyPropertiesWithList(source, target, 0);
    }

    /**
     * 递归执行Bean属性复制，同时处理List类型的属性，支持嵌套的List（最多5层）
     * @param source 源对象
     * @param target 目标对象
     * @param depth 当前递归深度
     */
    private static void copyPropertiesWithList(Object source, Object target, int depth) {
        // 如果递归深度超过最大值，直接返回
        if (depth > MAX_DEPTH) {
            return;
        }

        // 使用BeanUtils复制普通属性
        BeanUtils.copyProperties(source, target);

        // 遍历源对象的字段，找到类型为List的字段，并进行深度复制
        Field[] fields = source.getClass().getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true); // 设置私有字段可以访问
            try {
                // 如果字段类型是List
                if (List.class.isAssignableFrom(field.getType())) {
                    Object sourceList = field.get(source);
                    Object targetList = field.get(target);

                    // 如果源对象的List字段不为空
                    if (sourceList != null) {
                        // 如果目标对象的List字段为空或不可变，初始化为可变ArrayList
                        if (targetList == null || !(targetList instanceof ArrayList)) {
                            targetList = new ArrayList<>();
                            field.set(target, targetList);
                        }

                        // 清空目标List，并将源List中的每个元素复制到目标List
                        List<?> sourceListTyped = (List<?>) sourceList;
                        List<Object> targetListTyped = (List<Object>) targetList;

                        // 直接替换目标List的内容
                        targetListTyped.clear();

                        // 递归处理List中的每个元素
                        for (Object item : sourceListTyped) {
                            // 检查元素类型是否匹配
                            if (item instanceof List) {
                                // 如果子元素是List，则递归处理
                                List<Object> newItemList = new ArrayList<>();
                                targetListTyped.add(newItemList);
                                copyPropertiesWithList(item, newItemList, depth + 1); // 深度增加
                            } else {
                                // 处理普通对象，并进行类型转换
                                Object targetItem = convertItem(item, field.getGenericType());
                                targetListTyped.add(targetItem);
                            }
                        }
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 将元素从源对象类型转换到目标类型
     * @param sourceItem 源对象
     * @param targetType 目标类型
     * @return 目标对象
     */
    private static Object convertItem(Object sourceItem, java.lang.reflect.Type targetType) {
        // 获取目标类型
        if (targetType instanceof java.lang.reflect.ParameterizedType) {
            java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) targetType;
            // 获取目标类型的泛型参数
            Class<?> targetClass = (Class<?>) parameterizedType.getActualTypeArguments()[0];

            try {
                // 创建目标类型的实例
                Object targetInstance = targetClass.getDeclaredConstructor().newInstance();
                // 使用BeanUtils复制属性
                BeanUtils.copyProperties(sourceItem, targetInstance);
                return targetInstance;
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
        }

        return sourceItem; // 如果没有匹配的类型，返回原始对象
    }
}
