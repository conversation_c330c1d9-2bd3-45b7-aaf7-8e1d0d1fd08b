package net.airuima.sygd.piugin.util;

import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class TimeUtils {

    /**
     * 格式化时间
     *
     * @param text
     * @return
     */
    public static LocalDateTime formatText(String text) {
        if (ObjectUtils.isEmpty(text)) {
            return null;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            return LocalDateTime.parse(text, formatter);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
