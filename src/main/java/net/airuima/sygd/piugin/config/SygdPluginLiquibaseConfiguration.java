package net.airuima.sygd.piugin.config;

import liquibase.integration.spring.SpringLiquibase;
import net.airuima.config.SecurityConfiguration;
import org.springframework.boot.autoconfigure.liquibase.LiquibaseProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.sql.DataSource;

@Configuration
public class SygdPluginLiquibaseConfiguration {

    static {
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/validate-glue");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/get-step-info");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/save-step-info");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/query-mes-data");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/rollBake-container");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/client/steps/worksheet/**");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/work-sheet-clients/sn/status");
        SecurityConfiguration.PERMIT_URL.add("/plugins/api/work-sheet-clients/**");
    }

    @Bean
    @DependsOn("liquibase")
    public SpringLiquibase sygdLiquibase(DataSource dataSource, LiquibaseProperties liquibaseProperties) {
        SpringLiquibase liquibase = new SpringLiquibase();
        liquibase.setDataSource(dataSource);
        liquibase.setChangeLog("classpath:config/liquibase/rmes-sygd-plugin-master.xml");
        liquibase.setContexts(liquibaseProperties.getContexts());
        liquibase.setDefaultSchema(liquibaseProperties.getDefaultSchema());
        liquibase.setDropFirst(liquibaseProperties.isDropFirst());
        liquibase.setChangeLogParameters(liquibaseProperties.getParameters());
        liquibase.setShouldRun(liquibaseProperties.isEnabled());
        return liquibase;
    }
}