package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单订单信息
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Repository
public interface CustomWorkSheetClientOrderRepository extends LogicDeleteableRepository<CustomWorkSheetClientOrder>,
        EntityGraphJpaSpecificationExecutor<CustomWorkSheetClientOrder>, EntityGraphJpaRepository<CustomWorkSheetClientOrder, Long> {

    /**
     * 根据id查找工单订单信息
     *
     * @param id      主键id
     * @param deleted 删除标记
     * @return java.util.Optional<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder>  工单订单信息
     */
    @Query("select c from CustomWorkSheetClientOrder c where c.id = ?1 and c.deleted = ?2")
    Optional<CustomWorkSheetClientOrder> findByIdAndDeleted(Long id, Long deleted);

    /**
     * 根据工单ID和销售订单查询工单订单信息
     *
     * @param workSheetId 工单ID
     * @param clientCode  客户编码
     * @param deleted     是否删除
     * @return java.util.Optional<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 工单订单信息
     */
    @Query("""
            select c from CustomWorkSheetClientOrder c
            where c.workSheet.id = ?1 and c.clientCode = ?2 and c.deleted = ?3""")
    Optional<CustomWorkSheetClientOrder> findByWorkSheetIdAndClientCodeAndDeleted(Long workSheetId, String clientCode, Long deleted);

    /**
     * 根据工单ID查询工单订单信息
     *
     * @param id      主键id
     * @param deleted 删除标记
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder>  工单订单信息
     */
    @Query("select c from CustomWorkSheetClientOrder c where c.workSheet.id = ?1 and c.deleted = ?2")
    List<CustomWorkSheetClientOrder> findByWorkSheetIdAndDeleted(Long id, Long deleted);


}
