package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Repository
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Repository
public interface ContainerAdaptRepository extends LogicDeleteableRepository<Container>,
        EntityGraphJpaSpecificationExecutor<Container>, EntityGraphJpaRepository<Container, Long> {


    /**
     * 根据容器号获取容器信息
     *
     * @param code    容器号
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.Container> 容器
     **/
    @Query("select c from Container c where c.code = ?1 and c.deleted = ?2")
    Optional<Container> findByCodeAndDeleted(String code, Long deleted);

    /**
     * 根据容器id和删除标识获取容器信息
     * @param id 容器id
     * @param deleted 删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.Container> 容器
     */
    @Query("select c from Container c where c.id = ?1 and c.deleted = ?2")
    Optional<Container> findByIdAndDeleted(Long id, Long deleted);


}
