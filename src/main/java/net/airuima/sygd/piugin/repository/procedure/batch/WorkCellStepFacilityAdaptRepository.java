package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.domain.base.scene.WorkCellStepFacility;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工位工序设备Repository
 *
 * <AUTHOR>
 * @date 2020-12-21
 */
@Repository
public interface WorkCellStepFacilityAdaptRepository extends LogicDeleteableRepository<WorkCellStepFacility>,
        EntityGraphJpaSpecificationExecutor<WorkCellStepFacility>, EntityGraphJpaRepository<WorkCellStepFacility, Long> {

    /**
     * 根据工序id集合查询工位工序设备
     * @param ids 工序id集合
     * @param deleted 逻辑删除
     * @return java.util.List<net.airuima.domain.base.scene.WorkCellStepFacility> 工位工序设备列表
     */
    @Query("select w from WorkCellStepFacility w where w.step.id in ?1 and w.deleted = ?2")
    @FetchMethod
    List<WorkCellStepFacility> findByStepIdInAndDeleted(List<Long> ids, Long deleted);


}
