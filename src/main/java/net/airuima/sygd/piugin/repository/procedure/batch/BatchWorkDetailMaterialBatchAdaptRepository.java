package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface BatchWorkDetailMaterialBatchAdaptRepository extends LogicDeleteableRepository<BatchWorkDetailMaterialBatch>,
        EntityGraphJpaSpecificationExecutor<BatchWorkDetailMaterialBatch>, EntityGraphJpaRepository<BatchWorkDetailMaterialBatch, Long> {


    /**
     * 根据批量生产详情主键ID集合和删除标识获取批量生产详情物料批次
     * @param bathDetailIdList 批量生产详情主键ID集合
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch> 批量生产详情物料批次
     */
    @DataFilter(isSkip = true)
    @FetchMethod
    @Query("select b from BatchWorkDetailMaterialBatch b where b.batchWorkDetail.id in ?1 and b.deleted = ?2")
    List<BatchWorkDetailMaterialBatch> findByBatchWorkDetailIdInAndDeleted(List<Long> bathDetailIdList, Long deleted);


}
