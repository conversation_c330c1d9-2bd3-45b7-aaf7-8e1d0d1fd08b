package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.repository.LogicDeleteableRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器详情
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Repository
public interface ContainerDetailAdaptRepository extends LogicDeleteableRepository<ContainerDetail>,
        EntityGraphJpaSpecificationExecutor<ContainerDetail>, EntityGraphJpaRepository<ContainerDetail, Long> {

    /**
     * 通过容器编码、容器状态获取最新一条记录
     *
     * @param containerCode 容器编码
     * @param deleted       删除标识
     * @return java.util.Optional<net.airuima.domain.procedure.batch.ContainerDetail> 容器生产详情
     * @throws
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @DataFilter(isSkip = true)
    Optional<ContainerDetail> findTop1ByContainerCodeAndDeletedOrderByIdDesc(String containerCode, Long deleted);

}
