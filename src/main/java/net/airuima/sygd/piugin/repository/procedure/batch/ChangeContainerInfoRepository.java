package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.domain.procedure.batch.BatchWorkDetailMaterialBatch;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainerInfo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface ChangeContainerInfoRepository extends LogicDeleteableRepository<ChangeContainerInfo>,
        EntityGraphJpaSpecificationExecutor<ChangeContainerInfo>, EntityGraphJpaRepository<ChangeContainerInfo, Long> {

    @Query("select c from ChangeContainerInfo c where c.subWorkSheet.id = ?1 and c.deleted = ?2")
    Optional<ChangeContainerInfo> findBySubWorkSheetIdAndDeleted(Long subWorkSheetId, Long deleted);


}
