package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单客户信息
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Repository
public interface CustomWorkSheetClientRepository extends LogicDeleteableRepository<CustomWorkSheetClient>,
        EntityGraphJpaSpecificationExecutor<CustomWorkSheetClient>, EntityGraphJpaRepository<CustomWorkSheetClient, Long> {

    /**
     * 根据工单ID查询客户信息
     *
     * @param id      工单ID
     * @param deleted 是否删除
     * @return java.util.Optional<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 客户信息
     */
    @Query("select c from CustomWorkSheetClient c where c.id = ?1 and c.deleted = ?2")
    Optional<CustomWorkSheetClient> findByIdAndDeleted(Long id, Long deleted);


    /**
     * 根据工单ID和客户编码查询客户信息
     *
     * @param id      工单ID
     * @param code    客户编码
     * @param deleted 是否删除
     * @return java.util.Optional<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 客户信息
     */
    @Query("select c from CustomWorkSheetClient c where c.workSheet.id = ?1 and c.code = ?2 and c.deleted = ?3")
    Optional<CustomWorkSheetClient> findByWorkSheetIdAndCodeAndDeleted(Long id, String code, Long deleted);

    /**
     * 根据工单ID查询客户信息
     *
     * @param id      工单ID
     * @param deleted 是否删除
     * @return java.util.Optional<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 客户信息
     */
    @Query("select c from CustomWorkSheetClient c where c.workSheet.id = ?1 and c.deleted = ?2")
    List<CustomWorkSheetClient> findByWorkSheetIdAndDeleted(Long id, Long deleted);


}
