package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.config.annotation.DataFilter;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单容器关联Repository
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Repository
public interface WorkSheetContainerRepository extends LogicDeleteableRepository<WorkSheetContainer>,
        EntityGraphJpaSpecificationExecutor<WorkSheetContainer>, EntityGraphJpaRepository<WorkSheetContainer, Long> {


    /**
     * 根据子工单编码和容器号获取工单容器关联信息
     *
     * @param serialNumber 子工单编码
     * @param code         容器号
     * @param deleted      删除标识
     * @return net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer 工单容器关联
     */
    @Query("""
            select w from WorkSheetContainer w
            where w.subWorkSheet.serialNumber = ?1 and w.container.code = ?2 and w.deleted = ?3""")
    @DataFilter(isSkip = true)
    WorkSheetContainer findBySubWorkSheetSerialNumberAndContainerCodeAndDeleted(String serialNumber, String code, Long deleted);

    /**
     * 根据子工单编码和删除标识获取工单容器关联信息
     * @param serialNumber 子工单编码
     * @param deleted 删除标识
     * @return net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer 工单容器关联
     */
    @Query("select w from WorkSheetContainer w where w.subWorkSheet.serialNumber = ?1 and w.deleted = ?2")
    List<WorkSheetContainer> findBySubWorkSheetSerialNumberAndDeleted(String serialNumber, Long deleted);

    /**
     * 根据容器编码和删除标识获取工单容器关联信息
     * @param code 容器编码
     * @param deleted 删除标识
     * @return net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer 工单容器关联
     */
    @Query("select w from WorkSheetContainer w where w.container.code = ?1 and w.deleted = ?2")
    Optional<WorkSheetContainer> findByContainerCodeAndDeleted(String code, Long deleted);


}
