package net.airuima.sygd.piugin.repository.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaRepository;
import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.LogicDeleteableRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器转换Repository
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Repository
public interface ChangeContainerRepository extends LogicDeleteableRepository<ChangeContainer>,
        EntityGraphJpaSpecificationExecutor<ChangeContainer>, EntityGraphJpaRepository<ChangeContainer, Long> {

    /**
     * 根据工位id和删除标识获取可用容器
     * @param id 工位id
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer> 容器
     */
    @Query("select c from ChangeContainer c where c.workCell.id = ?1 and c.remainNumber > 0 and c.deleted = ?2")
    @FetchMethod
    List<ChangeContainer> findAvailableByWorkCellIdAndDeleted(Long id, Long deleted);

    /**
     * 根据工单id和工序id获取转换容器
     * @param workSheetId 工单id
     * @param stepId 工序id
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer> 转换容器
     */
    @Query("select c from ChangeContainer c where c.workSheet.id = ?1 and c.step.id = ?2 and c.remainNumber > 0 and c.deleted = ?3")
    @FetchMethod
    List<ChangeContainer> findByWorkSheetIdAndStepIdAndDeleted(Long workSheetId, Long stepId, Long deleted);

    @Query("select c from ChangeContainer c where c.subWorkSheet.id = ?1 and c.deleted != 0")
    @FetchMethod
    List<ChangeContainer> findDeleteChangeContainerBySubWorkSheetId(Long id);


    /**
     * 根据工单id和工序id获取转换容器
     * @param subWorkSheetId 子工单id
     * @param workCellId 工位id
     * @param containerId 容器id
     * @param stepId 工序id
     * @param deleted 删除标识
     * @return net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer 转换容器
     */
    @Query("""
            select c from ChangeContainer c
            where c.subWorkSheet.id = ?1 and c.workCell.id = ?2 and c.container.id = ?3 and c.step.id = ?4 and c.deleted = ?5 """)
    @FetchMethod
    ChangeContainer findBySubWorkSheetIdAndWorkCellIdAndContainerIdAndStepIdAndDeleted(Long subWorkSheetId, Long workCellId, Long containerId, Long stepId, Long deleted);

    /**
     * 根据工单id和工序id获取转换容器
     * @param subWorkSheetId 子工单id
     * @param workCellId 工位id
     * @param containerId 容器id
     * @param stepId 工序id
     * @return net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer 转换容器
     */
    @Query("""
            select c from ChangeContainer c
            where c.subWorkSheet.id = ?1 and c.workCell.id = ?2 and c.container.id = ?3 and c.step.id = ?4 and c.deleted != 0 """)
    @FetchMethod
   List<ChangeContainer> findByDeletedChangeHistory(Long subWorkSheetId, Long workCellId, Long containerId, Long stepId);


    /**
     * 根据id列表获取转换容器
     * @param ids id列表
     * @param deleted 删除标识
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer> 转换容器
     */
    @FetchMethod
    @Query("select c from ChangeContainer c where c.id in ?1 and c.deleted = ?2")
    List<ChangeContainer> findByIdInAndDeleted(List<Long> ids, Long deleted);


}
