package net.airuima.sygd.piugin.client.feign.ocmes;

import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDataDTO;
import net.airuima.util.ResponseData;
import org.springframework.http.ResponseEntity;
/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水批次FallBack
 *
 * <AUTHOR>
 * @date 2024-10-22
 */
public class GlueMaterialBatchAdaptFeignClientFallBack implements GlueMaterialBatchAdaptFeignClient {
    @Override
    public ResponseEntity<ResponseData<GlueMaterialBatchVerifyDataDTO>> validateGlue(String materialBatch) {
        return null;
    }
}
