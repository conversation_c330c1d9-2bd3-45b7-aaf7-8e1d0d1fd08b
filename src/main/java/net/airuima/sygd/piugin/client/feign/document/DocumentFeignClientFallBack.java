package net.airuima.sygd.piugin.client.feign.document;

import feign.Response;
import net.airuima.dto.document.DocumentDTO;
import net.airuima.util.ResponseData;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/11/3
 */
public class DocumentFeignClientFallBack implements DocumentFeignClient {


    @Override
    public ResponseEntity<ResponseData<DocumentDTO>> custom(MultipartFile file, int documentCategory, String name) {
        return null;
    }

    @Override
    public void deleted(Long id) {
        //Do nothing because of fallback method
    }

    @Override
    public Response download(String serviceName, Integer operation, Long documentId) {
        return null;
    }
}
