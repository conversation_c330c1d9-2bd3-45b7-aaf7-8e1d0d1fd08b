package net.airuima.sygd.piugin.client.feign.ocmes;

import net.airuima.client.AuthorizedFeignClient;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDataDTO;
import net.airuima.util.ResponseData;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/3/21
 */
@AuthorizedFeignClient(name = "ocmes", fallback = GlueMaterialBatchAdaptFeignClientFallBack.class, url = "${debug.feign.client.config.ocmes.url:}")
public interface GlueMaterialBatchAdaptFeignClient {

    /**
     * 验证胶水批次
     * @param materialBatch 胶水批次
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData< net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDataDTO>> 验证结果
     */
    @RequestMapping(value = "/api/glue-material-batches/validate-glue/{materialBatch}",method = RequestMethod.GET)
    ResponseEntity<ResponseData<GlueMaterialBatchVerifyDataDTO>> validateGlue(@PathVariable(value = "materialBatch") String materialBatch);
}
