package net.airuima.sygd.piugin.client.feign.document;

import feign.Response;
import net.airuima.client.AuthorizedFeignClient;
import net.airuima.dto.document.DocumentDTO;
import net.airuima.util.ResponseData;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * Copyright (C), 2017-2022, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2022/11/3
 */
@AuthorizedFeignClient(name = "document", fallback = DocumentFeignClientFallBack.class, url = "${debug.feign.client.config.document.url:}")
public interface DocumentFeignClient {
    /**
     * 新增文件
     *
     * @param file             文件
     * @param documentCategory 类型
     * @param name             名字
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData< net.airuima.dto.document.DocumentDTO>文档对象
     */
    @RequestMapping(value = "/api/documents/custom", method = RequestMethod.POST, consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    ResponseEntity<ResponseData<DocumentDTO>> custom(@RequestPart(value = "file") MultipartFile file, @RequestParam("documentCategory") int documentCategory, @RequestParam("name") String name);

    /**
     * 删除文件
     *
     * @param id 删除的id
     */
    @RequestMapping(value = "/api/documents/{id}", method = RequestMethod.DELETE)
    void deleted(@PathVariable("id") Long id);

    /**
     * 下载文件
     * @param serviceName 服务名字
     * @param operation 操作 0浏览 1下载
     * @param documentId 文档id
     * @return feign.Response 响应
     */
    @RequestMapping(value = "/api/documents/preview", method = RequestMethod.GET)
    Response download(@RequestParam("serviceName") String serviceName, @RequestParam("operation") Integer operation, @RequestParam("documentId") Long documentId);

}
