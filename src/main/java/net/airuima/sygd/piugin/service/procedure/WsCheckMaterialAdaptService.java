package net.airuima.sygd.piugin.service.procedure;


import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.client.feign.bom.MaterialFeignClient;
import net.airuima.client.feign.rule.SystemCodeFeignClient;
import net.airuima.constant.Constants;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.WsMaterial;
import net.airuima.domain.procedure.material.WsCheckMaterial;
import net.airuima.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.dto.base.BaseDTO;
import net.airuima.dto.bom.MaterialDTO;
import net.airuima.dto.material.CheckMaterialDetailDTO;
import net.airuima.dto.sync.SyncResultDTO;
import net.airuima.dto.sync.SyncWsCheckMaterialDTO;
import net.airuima.repository.procedure.aps.WorkSheetRepository;
import net.airuima.repository.procedure.batch.WsMaterialRepository;
import net.airuima.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.repository.procedure.material.WsCheckMaterialRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.service.procedure.material.IWsCheckMaterialDetailService;
import net.airuima.service.procedure.material.IWsCheckMaterialService;
import net.airuima.service.procedure.material.WsCheckMaterialService;
import net.airuima.util.ResponseDataUtils;
import net.airuima.util.ValidateUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料表Service
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WsCheckMaterialAdaptService extends CommonJpaService<WsCheckMaterial> {
    private final Logger log = LoggerFactory.getLogger(WsCheckMaterialService.class);
    private static final String WS_CHECK_MATERIAL_ENTITY_GRAPH = "wsCheckMaterialEntityGraph";
    private final WsCheckMaterialRepository wsCheckMaterialRepository;

    private final WorkSheetRepository workSheetRepository;

    private final WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    private final WsMaterialRepository wsMaterialRepository;

    private final MaterialFeignClient materialFeignClient;

    @Autowired
    private SystemCodeFeignClient systemCodeFeignClient;

    @Autowired
    private IWsCheckMaterialService[] iWsCheckMaterialServices;
    @Autowired
    private IWsCheckMaterialDetailService[] wsCheckMaterialDetailServices;

    public WsCheckMaterialAdaptService(WsCheckMaterialRepository wsCheckMaterialRepository,
                                       WorkSheetRepository workSheetRepository,
                                       WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository,
                                       WsMaterialRepository wsMaterialRepository,
                                       MaterialFeignClient materialFeignClient) {
        this.wsCheckMaterialRepository = wsCheckMaterialRepository;
        this.workSheetRepository = workSheetRepository;
        this.wsCheckMaterialDetailRepository = wsCheckMaterialDetailRepository;
        this.wsMaterialRepository = wsMaterialRepository;
        this.materialFeignClient = materialFeignClient;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WsCheckMaterial> find(Specification<WsCheckMaterial> spec, Pageable pageable) {
        return wsCheckMaterialRepository.findAll(spec, pageable, new NamedEntityGraph(WS_CHECK_MATERIAL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public List<WsCheckMaterial> find(Specification<WsCheckMaterial> spec) {
        return wsCheckMaterialRepository.findAll(spec, new NamedEntityGraph(WS_CHECK_MATERIAL_ENTITY_GRAPH));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<WsCheckMaterial> findAll(Pageable pageable) {
        return wsCheckMaterialRepository.findAll(pageable, new NamedEntityGraph(WS_CHECK_MATERIAL_ENTITY_GRAPH));
    }


    /**
     * 工单核料单同步
     *
     * @param syncWsCheckMaterialDtoList 核料单同步数据
     * @return List<SyncResultDTO>
     * <AUTHOR>
     * @date 2022/7/14
     */
    public List<SyncResultDTO> syncWsCheckMaterial(List<SyncWsCheckMaterialDTO> syncWsCheckMaterialDtoList) {

        //获取是否自动核料配置，若是自动核料则自动更新库存及核料记录
        String result = ResponseDataUtils.get(systemCodeFeignClient.findByCode(Constants.KEY_AUTO_CHECK_MATERIAL));
        boolean autoCheckMaterial = StringUtils.isNotBlank(result) && Boolean.parseBoolean(result);
        List<CheckMaterialDetailDTO> checkMaterialDetailDTOList = new ArrayList<>();
        List<SyncResultDTO> syncResultDtoList = Lists.newArrayList();
        if (ValidateUtils.isValid(syncWsCheckMaterialDtoList)) {
            syncWsCheckMaterialDtoList.forEach(syncWsCheckMaterialDto -> {
                Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(syncWsCheckMaterialDto.getSerialNumber(), Constants.LONG_ZERO);
                if (workSheetOptional.isEmpty()) {
                    syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, syncWsCheckMaterialDto.getSerialNumber() + " 当前工单不存在"));
                    return;
                }
                Optional<WsCheckMaterial> wsCheckMaterialOptional = wsCheckMaterialRepository.findByCodeAndWorkSheetId(syncWsCheckMaterialDto.getCheckMaterialCode(), workSheetOptional.get().getId());
                if (wsCheckMaterialOptional.isPresent()) {
                    // 存在则强制覆盖
                    forceDelete(wsCheckMaterialOptional.get().getId());
                }
                if (syncWsCheckMaterialDto.getCheckMaterialInfoList().isEmpty()) {
                    syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, " 核料信息详情不存在"));
                    return;
                }
                //获取当前工单 投料单
                List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheetOptional.get().getId(), Constants.LONG_ZERO);
                Map<Long, List<WsMaterial>> wsMaterialMap = wsMaterialList.stream().collect(Collectors.groupingBy(WsMaterial::getMaterialId));
                //获取物料相关信息
                List<MaterialDTO> materialDtoList = ResponseDataUtils.get(materialFeignClient.findByCodeList(syncWsCheckMaterialDto.getCheckMaterialInfoList().stream().map(SyncWsCheckMaterialDTO.CheckMaterialInfo::getMaterialCode).collect(Collectors.toList())));
                Map<String, List<MaterialDTO>> materialMap = materialDtoList.stream().collect(Collectors.groupingBy(MaterialDTO::getCode));
                //判断当前导入物料是否在投料单中存在
                boolean existNullMaterial = materialDtoList.stream().map(MaterialDTO::getId).allMatch(materialId -> wsMaterialMap.get(materialId) == null);
                if (existNullMaterial) {
                    syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_TWO, " 物料在投料单中不存在"));
                    return;
                }
                //新增&修改
                if (syncWsCheckMaterialDto.getOperate() == Constants.INT_ZERO || syncWsCheckMaterialDto.getOperate() == Constants.INT_ONE) {
                    WsCheckMaterial wsCheckMaterial = new WsCheckMaterial();
                    wsCheckMaterial.setWorkSheet(workSheetOptional.get()).setCode(syncWsCheckMaterialDto.getCheckMaterialCode());
                    wsCheckMaterial = wsCheckMaterialRepository.save(wsCheckMaterial);
                    //保存核料详情
                    WsCheckMaterial finalWsCheckMaterial = wsCheckMaterial;
                    syncWsCheckMaterialDto.getCheckMaterialInfoList().forEach(checkMaterialInfo -> {
                        WsCheckMaterialDetail wsCheckMaterialDetail = new WsCheckMaterialDetail();
                        wsCheckMaterialDetail.setWsCheckMaterial(finalWsCheckMaterial).setWorkSheet(workSheetOptional.get())
                                .setMaterialId(materialMap.get(checkMaterialInfo.getMaterialCode()).get(Constants.INT_ZERO).getId())
                                .setBatch(checkMaterialInfo.getMaterialBatch())
                                .setUncheckNumber(checkMaterialInfo.getMaterialNumber());
                        wsCheckMaterialDetail = wsCheckMaterialDetailRepository.save(wsCheckMaterialDetail);
                        if (autoCheckMaterial) {
                            checkMaterialDetailDTOList.add(new CheckMaterialDetailDTO().setId(wsCheckMaterialDetail.getId()).setNum(wsCheckMaterialDetail.getUncheckNumber()));
                        }
                    });
                    syncResultDtoList.add(new SyncResultDTO(syncWsCheckMaterialDto.getId(), Constants.INT_ONE, ""));
                }
            });
        }
        //如果是自动核料则直接自动进行核料和更新库存,即使失败不可以影响同步的事务
        try {
            if (autoCheckMaterial && !CollectionUtils.isEmpty(checkMaterialDetailDTOList)) {
                wsCheckMaterialDetailServices[0].saveCheckMaterialDetail(checkMaterialDetailDTOList);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return syncResultDtoList;
    }

    public BaseDTO forceDelete(Long id) {
        wsCheckMaterialDetailRepository.batchDeleteWsCheckMaterialDetailByWsCheckMaterialId(id);
        wsCheckMaterialRepository.logicDelete(id);
        return new BaseDTO(Constants.OK);
    }

    /**
     * 根据核料凭证判断是否存在 核料
     *
     * @param code 核料凭证
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/7/14
     */
    public Boolean validateWsCheckMaterialExist(String code) {
        List<WsCheckMaterial> wsCheckMaterialOptional = wsCheckMaterialRepository.findByCode(code);
        return ValidateUtils.isValid(wsCheckMaterialOptional);
    }

}
