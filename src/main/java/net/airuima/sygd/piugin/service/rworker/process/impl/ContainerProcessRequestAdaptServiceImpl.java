package net.airuima.sygd.piugin.service.rworker.process.impl;

import net.airuima.client.feign.organization.StaffFeignClient;
import net.airuima.client.feign.rule.DictionaryFeignClient;
import net.airuima.client.feign.rule.SystemCodeFeignClient;
import net.airuima.constant.Constants;
import net.airuima.constant.ConstantsEnum;
import net.airuima.constant.MaintainEnum;
import net.airuima.domain.base.pedigree.PedigreeStepSpecification;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.quality.UnqualifiedItem;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.domain.procedure.batch.WsStep;
import net.airuima.domain.procedure.maintaincase.MaintainHistory;
import net.airuima.domain.procedure.quality.CheckHistory;
import net.airuima.domain.procedure.quality.InspectTask;
import net.airuima.dto.base.BaseDTO;
import net.airuima.dto.base.BaseResultDTO;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.repository.base.process.StepRepository;
import net.airuima.repository.base.quality.OnlineReworkRuleRepository;
import net.airuima.repository.base.scene.WorkCellRepository;
import net.airuima.repository.base.scene.WorkCellStepRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.*;
import net.airuima.repository.procedure.maintaincase.MaintainHistoryRepository;
import net.airuima.repository.procedure.quality.CheckHistoryRepository;
import net.airuima.repository.procedure.quality.InspectTaskRepository;
import net.airuima.service.common.CommonService;
import net.airuima.service.ocmes.BakeCycleBakeAgeingModelService;
import net.airuima.service.rworker.cache.IRworkerCacheService;
import net.airuima.service.rworker.dynamic.IDynamicService;
import net.airuima.service.rworker.event.IEventService;
import net.airuima.service.rworker.facility.*;
import net.airuima.service.rworker.material.IMaterialService;
import net.airuima.service.rworker.process.*;
import net.airuima.service.rworker.process.impl.ContainerProcessRequestServiceImpl.ContainerNextToDoStepBaseInfo;
import net.airuima.service.rworker.quality.IEnvironmentService;
import net.airuima.service.rworker.quality.IQualityService;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerDetailAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkSheetContainerRepository;
import net.airuima.sygd.piugin.service.procedure.batch.ClientStepAdaptService;
import net.airuima.sygd.piugin.service.procedure.batch.ContainerAdaptService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerCheckDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.NumberUtils;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import net.airuima.web.rest.rworker.process.dto.*;
import net.airuima.web.rest.rworker.process.dto.general.FacilityGetInfo;
import net.airuima.web.rest.rworker.process.dto.general.ProcessDocumentGetInfo;
import net.airuima.web.rest.rworker.process.dto.general.WsStepGetInfo;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2023/1/31
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class ContainerProcessRequestAdaptServiceImpl implements IContainerProcessRequestService {
    private static final String SUB_WORK_SHEET_WAIT_FOR_ANALYZE = "subWorkSheetWaitForAnalyze";
    private static final String ERR_MSG = "容器处于待分析中!";
    private static final String ERR_MSG2 = "容器处于待维修中!";
    private static final String SUB_WORK_SHEET_WAIT_FOR_MAINTAIN = "subWorkSheetWaitForMaintain";
    private static final String REQUEST_CONTAINER_IS_NOT_BINDING = "requestContainerIsNotBinding";
    private static final String REQUEST_CONTAINER_IS_NOT_BINDING_MSG = "请求工序生产的容器尚未绑定";
    private static final Logger log = LoggerFactory.getLogger(ContainerProcessRequestAdaptServiceImpl.class);

    @Autowired
    private ContainerRepository containerRepository;
    @Autowired
    private ContainerDetailRepository containerDetailRepository;
    @Autowired
    private SystemCodeFeignClient systemCodeFeignClient;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkCellStepRepository workCellStepRepository;
    @Autowired
    private OnlineReworkRuleRepository onlineReworkRuleRepository;
    @Autowired
    private ContainerDetailUnqualifiedItemRepository containerDetailUnqualifiedItemRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private StaffFeignClient staffFeignClient;
    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private MaintainHistoryRepository maintainHistoryRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private InspectTaskRepository inspectTaskRepository;
    @Autowired
    private CheckHistoryRepository checkHistoryRepository;
    @Autowired
    private IBatchProcessRequestService[] batchProcessRequestServices;
    @Autowired
    private IMaterialService[] materialServices;
    @Autowired
    private IToDoStepStepValidateService[] toDoStepStepValidateServices;
    @Autowired
    private IEnvironmentService[] environmentServices;
    @Autowired
    private IFacilityService[] facilityServices;
    @Autowired
    private IFacilityCalibrateService[] facilityCalibrateServices;
    @Autowired
    private IFacilityInspectionService[] facilityInspectionServices;
    @Autowired
    private IFacilityMaintainService[] facilityMaintainServices;
    @Autowired
    private IQualityService[] qualityServices;
    @Autowired
    private IDynamicService[] dynamicServices;
    @Autowired
    private IToDoStepConfigService[] toDoStepConfigServices;
    @Autowired
    private IWearingPartService[] wearingPartServices;
    @Autowired
    private ISnProcessRequestService[] snProcessRequestServices;
    @Autowired
    private BakeCycleBakeAgeingModelService[] bakeCycleBakeAgeingModelServices;
    @Autowired
    private IEventService[] eventServices;
    @Autowired
    private DictionaryFeignClient dictionaryFeignClient;
    @Autowired
    private IRworkerCacheService[] rworkerCacheServices;

    @Autowired
    private ContainerAdaptService containerAdaptService;
    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private WorkSheetContainerRepository workSheetContainerRepository;
    @Autowired
    private ContainerDetailAdaptRepository containerDetailAdaptRepository;

    /**
     * 验证绑定的容器编码是否合规
     *
     * @param rworkerBindContainerValidateRequestDTO 验证待绑定容器合规性参数DTO
     * @return net.airuima.web.rest.rworker.process.dto.RworkerBindContainerValidateGetDTO 待绑定容器信息
     */
    @Override
    public RworkerBindContainerValidateGetDTO validateBindContainer(RworkerBindContainerValidateRequestDTO rworkerBindContainerValidateRequestDTO) {
        List<String> stepRequestContainerCodeList = rworkerBindContainerValidateRequestDTO.getStepRequestContainerCodeList();
        String bindContainerCode = rworkerBindContainerValidateRequestDTO.getBindContainerCode();
        // 只能使用提前与子工单绑定的容器
        BaseDTO baseDTO = containerAdaptService.verifyContainer(new ContainerCheckDTO(rworkerBindContainerValidateRequestDTO.getProductWorkSheetId(), bindContainerCode));
        if (baseDTO.getStatus().equals(Constants.KO)) {
            throw new ResponseException("error.verifySubWorkSheetContainerError", baseDTO.getMessage());
        }
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(bindContainerCode, Constants.LONG_ZERO);
        //绑定新容器时验证容器是否可用
        Container container = containerOptional.orElse(null);
        if (null == container) {
            container = new Container().setName(bindContainerCode).setCode(bindContainerCode).setStatus(Constants.FALSE);
            container.setDeleted(Constants.LONG_ZERO);
            container = containerRepository.save(container);
        }
        //如果待绑定的容器存在未解绑的容器详情且不属于容器复用的情况则提示容器不可进行绑定
        Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(container.getCode(), ConstantsEnum.BINDING.getCategoryName(), Constants.LONG_ZERO);
        containerDetailOptional.ifPresent(containerDetail -> {
            if (!ValidateUtils.isValid(stepRequestContainerCodeList) || !stepRequestContainerCodeList.contains(bindContainerCode)) {
                throw new ResponseException("error.containerIsBinding", "当前容器已被占用!");
            }
            //是否存在待检任务
            Optional<InspectTask> inspectTaskOptional = !ObjectUtils.isEmpty(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ?
                    inspectTaskRepository.findBySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(), containerDetail.getContainerCode(), Boolean.FALSE, Constants.LONG_ZERO) :
                    inspectTaskRepository.findByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getWorkSheet().getId(), containerDetail.getContainerCode(), Boolean.FALSE, Constants.LONG_ZERO);
            if (inspectTaskOptional.isPresent()) {
                throw new ResponseException("error.containerWaitInspectTask", "容器需要进行待检任务!");
            }

            Optional<CheckHistory> checkHistoryOptional = !ObjectUtils.isEmpty(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ?
                    checkHistoryRepository.findTop1BySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO) :
                    checkHistoryRepository.findTop1ByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO);
            if (checkHistoryOptional.isPresent()) {
                throw new ResponseException("error.containerWaitCheckHistory", "容器需要进行检测记录处理!");
            }
        });
        //验证容器是否处于维修分析完成
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateBindContainerMaintain(container.getId());
        return new RworkerBindContainerValidateGetDTO(container);
    }

    /**
     * 获取容器待做工序信息
     *
     * @param rworkerContainerToDoStepRequestDTO 容器请求工序生产参数
     * @return net.airuima.web.rest.rworker.process.dto.RworkerContainerToDoStepGetDTO 容器待做工序信息
     */
    @Override
    public RworkerContainerToDoStepGetDTO containerToDoStep(RworkerContainerToDoStepRequestDTO rworkerContainerToDoStepRequestDTO) {
        //获取系统配置的投产粒度(子工单或者工单)
        boolean subWsProductionMode = commonService.subWsProductionMode();
        RworkerContainerToDoStepGetDTO containerNextToDoStepTempInfo = new RworkerContainerToDoStepGetDTO();
        //验证请求生产的容器列表合规性
        ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo = this.validateRequestContainer(rworkerContainerToDoStepRequestDTO, containerNextToDoStepTempInfo, subWsProductionMode);
        //获取工位可生产的工序列表
        List<Step> stepList = workCellStepRepository.findByWorkCellId(rworkerContainerToDoStepRequestDTO.getWorkCellId(), Constants.LONG_ZERO);
        Step nextToDoStep = stepList.stream().filter(step -> containerNextToDoStepBaseInfo.getNextTodoStepIdList().contains(step.getId())).findFirst().orElse(null);
        if (null == nextToDoStep) {
            if (!CollectionUtils.isEmpty(containerNextToDoStepBaseInfo.getNextTodoStepIdList())) {
                Step step = stepRepository.getReferenceById(containerNextToDoStepBaseInfo.getNextTodoStepIdList().get(Constants.INT_ZERO));
                throw new ResponseException("error.notExistTodoStep", "容器待做工序为【" + step.getName() + ",当前工位不可生产该工序");
            }
            throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
        }
        WsStep nextToDoWsStep = containerNextToDoStepBaseInfo.getWsStepList().stream().filter(wsStep -> wsStep.getStep().getId().equals(nextToDoStep.getId())).findFirst().orElse(new WsStep());
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        if (Boolean.TRUE.equals(nextToDoWsStep.getIsControlMaterial())) {
            //检查工单是否已核料可继续投产
            materialServices[0].validateReceivedMaterial(containerNextToDoStepBaseInfo.getWorkSheet());
        }
        WorkSheet workSheet = containerNextToDoStepBaseInfo.getWorkSheet();
        //初始化请求生产工序后的通用信息
        RworkerStepProcessBaseDTO stepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        StaffDTO staffDTO = staffFeignClient.findById(rworkerContainerToDoStepRequestDTO.getStaffId());
        WorkCell workCell = workCellRepository.getReferenceById(rworkerContainerToDoStepRequestDTO.getWorkCellId());
        WorkFlow workFlow = null != nextToDoWsStep.getWorkFlow() ? nextToDoWsStep.getWorkFlow() : containerNextToDoStepBaseInfo.getWorkSheet().getWorkFlow();
        stepProcessBaseDTO.setWsStepList(containerNextToDoStepBaseInfo.getWsStepList()).setWorkFlow(workFlow).setStaffDTO(staffDTO).setStep(nextToDoStep).setWsStep(nextToDoWsStep).setWorkSheet(containerNextToDoStepBaseInfo.getWorkSheet()).setSubWorkSheet(containerNextToDoStepBaseInfo.getSubWorkSheet()).setSubWsProductionMode(subWsProductionMode).setWorkCell(workCell);
        RworkerContainerToDoStepGetDTO containerNextToDoStepInfo = new RworkerContainerToDoStepGetDTO(nextToDoWsStep);

        // 设置数量
        if (containerNextToDoStepTempInfo.getContainerInfo() != null) {
            //验证工序时间间隔是否合规
            BaseResultDTO baseResultDTO = toDoStepStepValidateServices[0].validateContainerStepInterval(stepProcessBaseDTO, containerNextToDoStepTempInfo.getContainerInfo().getId(), subWsProductionMode);
            if (Objects.nonNull(baseResultDTO) && baseResultDTO.getStatus().equals(Constants.KO)) {
                throw new ResponseException(baseResultDTO.getKey(), baseResultDTO.getMessage());
            }
        }
        //判断当前投产工序是否存在转工艺或者为在线返工
        if (workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName() || !nextToDoWsStep.getWorkFlow().getId().equals(workSheet.getWorkFlow().getId())) {
            containerNextToDoStepInfo.setOnlineMaintainOrTransferWorkFlow(Boolean.TRUE);
        }
        //验证指定工单工序工位是否合规
        toDoStepStepValidateServices[0].validWsStepWorkCell(stepProcessBaseDTO);
        //验证员工技能是否匹配当前待做工序
        toDoStepStepValidateServices[0].validateStaffSkill(stepProcessBaseDTO);
        //验证工位GRR是否合规
        toDoStepStepValidateServices[0].validateWorkCellGrr(stepProcessBaseDTO);
        //验证环境-温湿度
        BaseWaringDTO baseWaringDTO = environmentServices[0].validateHumiture(stepProcessBaseDTO);
        if (null != baseWaringDTO) {
            containerNextToDoStepInfo.setKey(baseWaringDTO.getKey()).setMessage(baseWaringDTO.getMessage());
        }
        containerNextToDoStepInfo.setWorkFlowId(workFlow.getId());
        //验证环境-洁净度
        environmentServices[0].validateCleanliness(stepProcessBaseDTO);
        containerNextToDoStepInfo.setContainerInfo(containerNextToDoStepTempInfo.getContainerInfo()).setWorkSheetInfo(containerNextToDoStepTempInfo.getWorkSheetInfo());
        //获取流转数
        BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).getNextToDoStepNumber(containerNextToDoStepInfo, containerNextToDoStepBaseInfo, nextToDoWsStep, nextToDoStep);
        //获取工位工序设备信息
        List<FacilityGetInfo> facilityGetInfoList = facilityServices[0].findStepFacilityInfo(rworkerContainerToDoStepRequestDTO.getWorkCellId(), nextToDoStep.getId());
        containerNextToDoStepInfo.setFacilityInfoList(facilityGetInfoList);
        stepProcessBaseDTO.setFacilityGetInfoList(facilityGetInfoList);
        List<Long> facilityIdList = CollectionUtils.isEmpty(facilityGetInfoList) ? null : facilityGetInfoList.stream().map(FacilityGetInfo::getId).collect(Collectors.toList());
        //验证请求的待做工序的基础信息是否存在未处理的事件
        eventServices[0].validateRequestStepExistUnProcessedEvent(stepProcessBaseDTO);
        //验证工位设备校准
        facilityCalibrateServices[0].validateFacilityCalibrate(rworkerContainerToDoStepRequestDTO.getWorkCellId(), facilityIdList);
        //验证设备点巡检等是否合规
        facilityInspectionServices[0].validateFacilityInspection(facilityIdList);
        //验证设备是否存在逾期维保任务
        facilityMaintainServices[0].validateFacilityMaintain(facilityIdList);
        //获取工序不良信息
        containerNextToDoStepInfo.setUnqualifiedItemInfoList(qualityServices[0].findStepUnqualifiedItemInfo(nextToDoStep.getId(), containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow.getId(), containerNextToDoStepBaseInfo.getWorkSheet().getClientId()));
        //获取BOM物料清单列表
        containerNextToDoStepInfo.setBomMaterialInfoList(materialServices[0].findBomMaterialInfo(containerNextToDoStepBaseInfo.getWorkSheet().getId()));
        //返回工单工序快照
        containerNextToDoStepInfo.setWsStepInfoList(containerNextToDoStepTempInfo.getWsStepInfoList());
        //设置工序动态数据信息
        containerNextToDoStepInfo.setStepDynamicDataGetDto(dynamicServices[0].getStepDynamicDataInfo(containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow, nextToDoStep));
        if (containerNextToDoStepTempInfo.getContainerInfo() != null) {
            containerNextToDoStepInfo.setStepDynamicDataGetVisibleDtoList(dynamicServices[0].getContainerStepDynamicDataVisibleInfo(containerNextToDoStepBaseInfo.getSubWorkSheet(), containerNextToDoStepBaseInfo.getWorkSheet(), nextToDoStep, containerNextToDoStepTempInfo.getContainerInfo().getId()));
        }
        //工序生产过程物料库存管控级别(0:不管控物料库存;1:工单物料库存;2:工位物料库存)
        int materialControlLevel = commonService.getMaterialControlLevel();
        containerNextToDoStepInfo.setIsFeedingMaterial(nextToDoWsStep.getIsControlMaterial()).setMaterialControlLevel(materialControlLevel);
        //获取工序技术指标及SOP
        PedigreeStepSpecification pedigreeStepSpecification = toDoStepConfigServices[0].findStepSpecificationSop(containerNextToDoStepBaseInfo.getWorkSheet().getPedigree(), workFlow, nextToDoStep, containerNextToDoStepBaseInfo.getWorkSheet().getClientId());
        if (null != pedigreeStepSpecification) {
            //设置技术指标
            containerNextToDoStepInfo.setSpecification(pedigreeStepSpecification.getQualification());
            //设置工序SOP信息
            if (ValidateUtils.isValid(pedigreeStepSpecification.getDocumentDTOList())) {
                List<ProcessDocumentGetInfo> stepSopInfoList = Lists.newArrayList();
                pedigreeStepSpecification.getDocumentDTOList().forEach(documentDTO -> stepSopInfoList.add(new ProcessDocumentGetInfo(documentDTO)));
                containerNextToDoStepInfo.setStepSopInfoList(stepSopInfoList);
            }
        }
        //获取当前待做工序的易损件信息
        containerNextToDoStepInfo.setWearingPartGroupInfoList(wearingPartServices[0].getWearingPartInfo(stepProcessBaseDTO));
        if (containerNextToDoStepInfo.getContainerInfo() != null) {
            //获取容器在当前工序可投产的SN信息列表
            snProcessRequestServices[0].findToDoContainerSn(containerNextToDoStepInfo.getContainerInfo().getId(), containerNextToDoStepInfo, stepProcessBaseDTO);
        }
        //容器烘烤温循相关
        if (containerNextToDoStepInfo.getContainerInfo() != null) {
            bakeCycleBakeAgeingModelServices[0].bakeCycleBakeAgeingContainerInfo(containerNextToDoStepInfo, stepProcessBaseDTO);
        }
        //请求工序时更新设备状态为正常运行
        facilityServices[0].updateFacilityStatus(facilityIdList, ConstantsEnum.FACILITY_STATUS_RUNNING.getCategoryName());
        if(CollectionUtils.isEmpty(containerNextToDoStepInfo.getWsStepInfoList())){
            containerNextToDoStepInfo.setWsStepInfoList(containerNextToDoStepBaseInfo.getWsStepList());
        }
        if(containerNextToDoStepInfo.getWorkSheetInfo() == null){
            RworkerToDoWsGetDTO rworkerToDoWsGetDTO = new RworkerToDoWsGetDTO(containerNextToDoStepBaseInfo.getSubWorkSheet());
            containerNextToDoStepInfo.setWorkSheetInfo(rworkerToDoWsGetDTO);
        }
        if(containerNextToDoStepInfo.getContainerInfo() == null){
            String requestContainerCode = rworkerContainerToDoStepRequestDTO.getRequestContainerCode();
            Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(requestContainerCode, Constants.LONG_ZERO);
            if(containerOptional.isPresent()){
                Container container = containerOptional.get();
                containerNextToDoStepInfo.setContainerInfo( new RworkerContainerToDoStepGetDTO.ContainerInfo(container));
            }

        }
        return containerNextToDoStepInfo;
    }

    /**
     * 计算当前容器投产数
     *
     * @param containerNextToDoStepInfo     容器待做工序信息
     * @param containerNextToDoStepBaseInfo 待做工序基础信息
     * @param nextToDoWsStep                待做工序定制工序
     * @param nextToDoStep                  待做工序
     * <AUTHOR>
     * @date 2023/3/18
     **/
    @Override
    public void getNextToDoStepNumber(RworkerContainerToDoStepGetDTO containerNextToDoStepInfo, ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo, WsStep nextToDoWsStep, Step nextToDoStep) {
        //下个待做工序投产数
        int nextToDoStepNumber = containerNextToDoStepBaseInfo.getTransferNumber();
        //若下个待做工序类型为调整工序则需要根据调整规则获取投产数
        if (nextToDoWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_REWORK_CATEGORY.getCategoryName() || nextToDoWsStep.getCategory() == ConstantsEnum.STEP_ONLINE_PRE_REWORK_CATEGORY.getCategoryName()) {
            nextToDoStepNumber = this.findOnlineReworkStepTransferNumber(nextToDoStep.getId(), containerNextToDoStepBaseInfo.getLatestContainerDetail().getId());
        }
        //投产数需要乘以投产比例作为最终投产数(小数向上取整)
        nextToDoStepNumber = (int) Math.ceil(NumberUtils.multiply(nextToDoStepNumber, nextToDoWsStep.getInputRate()).doubleValue());
        containerNextToDoStepInfo.setNumber(nextToDoStepNumber);
    }

    /**
     * 获取容器请求模式下的在线调整工序的投产数量
     *
     * @param stepId             待做工序主键ID
     * @param requestContainerId 请求容器主键ID
     * @return int 在线调整数量
     */
    @Override
    public int findOnlineReworkStepTransferNumber(long stepId, Long requestContainerId) {
        List<UnqualifiedItem> unqualifiedItemList = onlineReworkRuleRepository.findUnqualifiedItemByStepIdAndDeleted(stepId, Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(unqualifiedItemList)) {
            throw new ResponseException("error.onlineReworkRuleNotExist", "在线调整工序未配置调整规则");
        }
        Long sumNpiNumber = containerDetailUnqualifiedItemRepository.findSumNumberByContainerDetailIdAndUnqualifiedItemInAndDeleted(requestContainerId, unqualifiedItemList.stream().map(UnqualifiedItem::getId).collect(Collectors.toList()), Constants.LONG_ZERO);
        return null != sumNpiNumber ? sumNpiNumber.intValue() : Constants.INT_ZERO;
    }

    /**
     * 验证请求生产的容器列表合规性
     *
     * @param containerToDoStepRequestDTO    容器请求工序参数DTO
     * @param rworkerContainerToDoStepGetDTO 容器请求工序返回DTO
     * @param subWsProductionMode            系统配置的投产粒度(子工单或者工单)
     * @return ContainerNextToDoStepBaseInfo
     */
    private ContainerNextToDoStepBaseInfo validateRequestContainer(RworkerContainerToDoStepRequestDTO containerToDoStepRequestDTO, RworkerContainerToDoStepGetDTO rworkerContainerToDoStepGetDTO, boolean subWsProductionMode) {

        String requestContainerCode = containerToDoStepRequestDTO.getRequestContainerCode();
        Container container = containerRepository.findByCodeAndDeleted(requestContainerCode, Constants.LONG_ZERO).orElse(null);
        if (null == container) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, "请求工序生产的容器中存在尚未绑定的容器");
        }
        // 判断是第一道工序
        Boolean isFirstContainerStep = false;
        // 查询容器详情
        Optional<ContainerDetail> containerDetailLatest = containerDetailAdaptRepository.findTop1ByContainerCodeAndDeletedOrderByIdDesc(container.getCode(), Constants.LONG_ZERO);
        // 存在不存在容器详情
        if (containerDetailLatest.isEmpty()) {
            // 查询批量生产详情 是否只存在第一道工序的生产详情 有其他工序说明当前不是第一道工序
            // 查询容器预绑定
            Optional<WorkSheetContainer> workSheetContainerOptional = workSheetContainerRepository.findByContainerCodeAndDeleted(requestContainerCode, Constants.LONG_ZERO);
            if (workSheetContainerOptional.isEmpty()) {
                throw new ResponseException("error.ContainerNotBind", "容器未绑定子工单");
            }
            WorkSheetContainer workSheetContainer = workSheetContainerOptional.get();
            SubWorkSheet subWorkSheet = workSheetContainer.getSubWorkSheet();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(wsStepList)) {
                wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            }
            //默认待投产工序列表为工单的第一个工序
            List<Long> firstStepIdList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(wsStep -> wsStep.getStep().getId()).toList();
            if (!CollectionUtils.isEmpty(firstStepIdList)) {
                Long firstStep = firstStepIdList.get(Constants.INT_ZERO);
                Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), firstStep, Constants.LONG_ZERO);
                // 生产详情为空 肯定是第一道工序
                if (batchWorkDetailOptional.isEmpty()) {
                    isFirstContainerStep = true;
                } else {
                    // 判断第一工序是否完成 没完成 当前容器继续请求第一道工序
                    BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.get();
                    // 第一个生产详情没完成 继续返回第一道工序数据
                    if (batchWorkDetail.getFinish() == Constants.INT_ZERO) {
                        isFirstContainerStep = true;
                    }
                }
            }
        }
        if (isFirstContainerStep) {
            ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo = new ContainerNextToDoStepBaseInfo();
            // 查询容器预绑定
            Optional<WorkSheetContainer> workSheetContainerOptional = workSheetContainerRepository.findByContainerCodeAndDeleted(requestContainerCode, Constants.LONG_ZERO);
            if (workSheetContainerOptional.isEmpty()) {
                throw new ResponseException("error.ContainerNotBind", "容器未绑定子工单");
            }
            WorkSheetContainer workSheetContainer = workSheetContainerOptional.get();
            // 获取子工单
            SubWorkSheet subWorkSheet = workSheetContainer.getSubWorkSheet();
            WorkSheet workSheet = subWorkSheet.getWorkSheet();
            List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (!ValidateUtils.isValid(wsStepList)) {
                wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            }
            //默认待投产工序列表为工单的第一个工序
            List<Long> nextToDoStepIdList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList());
            if (workSheetContainer.getCustom2() != null) {
                containerNextToDoStepBaseInfo.setTransferNumber(Integer.parseInt(workSheetContainer.getCustom2()));
            }
            return containerNextToDoStepBaseInfo.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setNextTodoStepIdList(nextToDoStepIdList).setWsStepList(wsStepList).setLatestContainerDetail(null);
        } else {
            //验证请求的容器是否存在缓存，如果有缓存则需要提示
            rworkerCacheServices[0].validateCacheWhenRequestTodoStep(Constants.INT_TWO, container.getCode());
            ContainerDetail containerDetail = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeleted(container.getCode(), Constants.INT_ONE, Constants.LONG_ZERO);
            WorkSheet workSheet;
            SubWorkSheet subWorkSheet;
            RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
            ContainerNextToDoStepBaseInfo containerNextToDoStepBaseInfo = new ContainerNextToDoStepBaseInfo();
            //查询不到绑定状态的容器详情时则需要进一步获取维修历史的工单及判断容器是否可以投产
            if (null == containerDetail) {
                //验证容器是否处于维修分析完成
                BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateRequestContainerMaintain(subWsProductionMode, Boolean.FALSE, container.getId(), rworkerStepProcessBaseDTO);
                ContainerDetail latestUnBindingContainerDetail = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(container.getCode(), ConstantsEnum.UNBIND.getCategoryName(), Constants.LONG_ZERO).orElse(new ContainerDetail());
                workSheet = rworkerStepProcessBaseDTO.getWorkSheet();
                subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
                containerNextToDoStepBaseInfo.setTransferNumber(latestUnBindingContainerDetail.getInputNumber());
            } else {
                //验证容器是否处于维修分析完成
                BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateRequestContainerMaintain(subWsProductionMode, Boolean.TRUE, container.getId(), rworkerStepProcessBaseDTO);
                Optional<MaintainHistory> maintainHistoryOptional = maintainHistoryRepository.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerDetail.getContainer().getId(), Constants.LONG_ZERO);
                maintainHistoryOptional.ifPresent(maintainHistory -> {
                    if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
                        throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
                    }
                    if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
                        throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
                    }
                });
                workSheet = subWsProductionMode ? containerDetail.getBatchWorkDetail().getSubWorkSheet().getWorkSheet() : containerDetail.getBatchWorkDetail().getWorkSheet();
                subWorkSheet = subWsProductionMode ? containerDetail.getBatchWorkDetail().getSubWorkSheet() : null;
                containerNextToDoStepBaseInfo.setTransferNumber(containerDetail.getTransferNumber());
            }
            List<WsStep> wsStepList = null != subWorkSheet ? wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO) : null;
            if (!ValidateUtils.isValid(wsStepList)) {
                wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            }
            if (null == wsStepList) {
                throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
            }
            //验证工单状态
            if (null != subWorkSheet) {
                batchProcessRequestServices[0].validateSubWorkSheetStatus(subWorkSheet);
            } else {
                batchProcessRequestServices[0].validateWorkSheetStatus(workSheet);
            }
            RworkerToDoWsGetDTO rworkerToDoWsGetDTO;
            if (subWsProductionMode) {
                assert subWorkSheet != null;
                rworkerToDoWsGetDTO = new RworkerToDoWsGetDTO(subWorkSheet);
            } else {
                rworkerToDoWsGetDTO = new RworkerToDoWsGetDTO(workSheet);
            }
            rworkerContainerToDoStepGetDTO.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
            rworkerContainerToDoStepGetDTO.setWorkSheetInfo(rworkerToDoWsGetDTO);
            rworkerContainerToDoStepGetDTO.setContainerInfo(new RworkerContainerToDoStepGetDTO.ContainerInfo(container));
            //默认待投产工序列表为工单的第一个工序
            List<Long> nextToDoStepIdList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(wsStep -> wsStep.getStep().getId()).collect(Collectors.toList());
            //若当前容器处于绑定状态则根据容器最新完成的工序及工单工序快照获取下个待投产工序列表
            if (null != containerDetail) {
                //是否存在待检任务
                Optional<InspectTask> inspectTaskOptional = subWsProductionMode ?
                        inspectTaskRepository.findBySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeAndStatusAndDeleted(subWorkSheet.getId(), containerDetail.getContainerCode(), Boolean.FALSE, Constants.LONG_ZERO) :
                        inspectTaskRepository.findByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeAndStatusAndDeleted(workSheet.getId(), containerDetail.getContainerCode(), Boolean.FALSE, Constants.LONG_ZERO);
                if (inspectTaskOptional.isPresent()) {
                    throw new ResponseException("error.containerWaitInspectTask", "容器需要进行待检任务!");
                }
                Optional<CheckHistory> checkHistoryOptional = !ObjectUtils.isEmpty(containerDetail.getBatchWorkDetail().getSubWorkSheet()) ?
                        checkHistoryRepository.findTop1BySubWorkSheetIdAndWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getSubWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO) :
                        checkHistoryRepository.findTop1ByWorkSheetIdAndSubWorkSheetIsNullAndContainerCodeAndCategoryGreaterThanAndStatusAndDeleted(containerDetail.getBatchWorkDetail().getWorkSheet().getId(), containerDetail.getContainerCode(), Constants.INT_ONE, Boolean.FALSE, Constants.LONG_ZERO);
                if (checkHistoryOptional.isPresent()) {
                    throw new ResponseException("error.containerWaitCheckHistory", "容器需要进行检测记录处理!");
                }

                WsStep latestFinishedWsStep = wsStepList.stream().filter(wsStep -> containerDetail.getBatchWorkDetail().getStep().getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
                if (null == latestFinishedWsStep || StringUtils.isBlank(latestFinishedWsStep.getAfterStepId())) {
                    throw new ResponseException("error.afterStepNotExist", "请求容器不存在后置待生产工序");
                }
                nextToDoStepIdList = Arrays.stream(latestFinishedWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).collect(Collectors.toList());
            }
            return containerNextToDoStepBaseInfo.setWorkSheet(workSheet).setSubWorkSheet(subWorkSheet).setNextTodoStepIdList(nextToDoStepIdList).setWsStepList(wsStepList).setLatestContainerDetail(containerDetail);
        }

    }

    /**
     * 维修分析的容器请求生产工序时验证维修数据获取对应投产工单
     *
     * @param subWsProductionMode       投产粒度(true:子工单,false:工单)
     * @param isBindingContainerRequest 是否为绑定状态的容器请求工序
     * @param containerId               请求容器主键ID
     * @param rworkerStepProcessBaseDTO 生产过程通用基础新信息
     */
    @Override
    public void validateRequestContainerMaintain(boolean subWsProductionMode, boolean isBindingContainerRequest, long containerId, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        //通过容器获取最新容器维修记录
        MaintainHistory maintainHistory = maintainHistoryRepository.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO).orElse(null);
        //容器维修记录为空且容器不存在绑定的容器详情则直接返回
        if (null == maintainHistory && !isBindingContainerRequest) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
        }
        if (null != maintainHistory && maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
        }
        if (null != maintainHistory && maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
        }
        if (null != maintainHistory && !isBindingContainerRequest && maintainHistory.getStatus() == MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus()) {
            rworkerStepProcessBaseDTO.setWorkSheet(maintainHistory.getWsRework().getReworkWorkSheet());
            rworkerStepProcessBaseDTO.setWorkFlow(rworkerStepProcessBaseDTO.getWorkSheet().getWorkFlow());
            //维修历史的工单若已取消或者完成时需要返回提示信息
            throwPromptMsg(subWsProductionMode, rworkerStepProcessBaseDTO);
        } else if (!isBindingContainerRequest) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, "请求工序生产的容器中存在尚未绑定的容器");
        }

    }

    /**
     * 维修历史的工单若已取消或者完成时需要返回提示信息
     *
     * @param subWsProductionMode       投产粒度
     * @param rworkerStepProcessBaseDTO 工序生产过程相关参数类
     */
    private void throwPromptMsg(boolean subWsProductionMode, RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO) {
        if (subWsProductionMode) {
            SubWorkSheet subWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(rworkerStepProcessBaseDTO.getWorkSheet().getId(), Constants.LONG_ZERO).orElseThrow(() -> new ResponseException("error.requestContainerSubWorkSheetNotExist", "请求容器对应子工单不存在"));
            rworkerStepProcessBaseDTO.setSubWorkSheet(subWorkSheet);
            if (rworkerStepProcessBaseDTO.getSubWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || rworkerStepProcessBaseDTO.getSubWorkSheet().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
                throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
            }
        } else if (rworkerStepProcessBaseDTO.getWorkSheet().getStatus() >= ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName() || rworkerStepProcessBaseDTO.getWorkSheet().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_CANCEL.getCategoryName()) {
            throw new ResponseException(REQUEST_CONTAINER_IS_NOT_BINDING, REQUEST_CONTAINER_IS_NOT_BINDING_MSG);
        }
    }

    /**
     * 绑定容器时验证状态
     *
     * @param containerId 容器主键ID
     */
    @Override
    public void validateBindContainerMaintain(long containerId) {
        MaintainHistory maintainHistory = maintainHistoryRepository.findTop1ByContainerDetailContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO).orElse(null);
        if (null == maintainHistory) {
            return;
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_ANALYZE_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_ANALYZE, ERR_MSG);
        }
        if (maintainHistory.getStatus() == MaintainEnum.WAIT_MAINTAIN_STATUS.getStatus()) {
            throw new ResponseException(SUB_WORK_SHEET_WAIT_FOR_MAINTAIN, ERR_MSG2);
        }
        if (maintainHistory.getStatus() == MaintainEnum.MAINTAIN_FINISHED_STATUS.getStatus() && maintainHistory.getResult() == MaintainEnum.MAINTAIN_RESULT_REWORK.getStatus()) {
            //获取系统配置的投产粒度(子工单或者工单)
            boolean subWsProductionMode = commonService.subWsProductionMode();
            List<WsStep> reWorkSheetStepList = wsStepRepository.findByWorkSheetIdAndDeleted(maintainHistory.getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO);
            List<WsStep> firstWsStepList = reWorkSheetStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).collect(Collectors.toList());
            if (firstWsStepList.stream().anyMatch(wsStep -> wsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName())) {
                Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(containerId, Constants.LONG_ZERO);
                containerDetailOptional.ifPresent(containerDetail -> {
                    if (!subWsProductionMode && !containerDetail.getBatchWorkDetail().getWorkSheet().getId().equals(maintainHistory.getWsRework().getReworkWorkSheet().getId())) {
                        throw new ResponseException("error.containerInReworkSheet", "容器在返工单中尚未下交!");
                    }
                    SubWorkSheet subWorkSheet = subWorkSheetRepository.findTop1ByWorkSheetIdAndDeleted(maintainHistory.getWsRework().getReworkWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
                    if (subWsProductionMode && null != subWorkSheet && !containerDetail.getBatchWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                        throw new ResponseException("error.containerInReworkSheet", "容器在返工单中尚未下交!");
                    }
                });
            }
        }
    }


}
