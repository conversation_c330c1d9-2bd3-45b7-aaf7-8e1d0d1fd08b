package net.airuima.sygd.piugin.service.procedure.aps.plugin.impl;

import net.airuima.client.feign.bom.BomFeignClient;
import net.airuima.client.feign.flowable.ProcessTaskFeign;
import net.airuima.client.feign.rmps.api.IWorkSheetSyncMpsFeignClient;
import net.airuima.client.feign.rule.DictionaryFeignClient;
import net.airuima.client.feign.rule.SerialNumberHistoryFeignClient;
import net.airuima.client.feign.rwms.api.IShipmentOrderFeignClient;
import net.airuima.constant.Constants;
import net.airuima.constant.ConstantsEnum;
import net.airuima.constant.WsEnum;
import net.airuima.domain.base.pedigree.Pedigree;
import net.airuima.domain.base.pedigree.PedigreeConfig;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.scene.WorkLine;
import net.airuima.domain.procedure.aps.SaleOrder;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.dto.aps.WorkSheetDTO;
import net.airuima.dto.base.BaseDTO;
import net.airuima.dto.bom.BomDTO;
import net.airuima.dto.flowable.FlowableResultDTO;
import net.airuima.dto.flowable.FlowableTaskCompleteDTO;
import net.airuima.dto.process.WorkFlowDTO;
import net.airuima.dto.rule.SerialNumberHistoryDTO;
import net.airuima.repository.base.pedigree.PedigreeRepository;
import net.airuima.repository.base.process.WorkFlowRepository;
import net.airuima.repository.base.scene.WorkLineRepository;
import net.airuima.repository.procedure.aps.SaleOrderRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.aps.WorkSheetRepository;
import net.airuima.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.repository.procedure.batch.WsStepRepository;
import net.airuima.service.base.process.WorkFlowStepService;
import net.airuima.service.common.CommonService;
import net.airuima.service.procedure.aps.SubWorkSheetService;
import net.airuima.service.procedure.aps.WorkSheetService;
import net.airuima.service.procedure.aps.dto.WorkSheetResDTO;
import net.airuima.service.procedure.aps.plugin.IWorkSheetService;
import net.airuima.service.procedure.aps.plugin.impl.WorkSheetServiceImpl;
import net.airuima.service.procedure.material.IWsMaterialService;
import net.airuima.service.report.api.IWorkSheetStepStatisticsService;
import net.airuima.util.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.Executors;

/**
 * Copyright(C), 2017-2021,武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @create 2023/4/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class WorkSheetAdaptServiceImpl implements IWorkSheetService {

    private static final String FLOWABLE_REAPPLY = "reApply";

    @Autowired
    private WorkSheetService workSheetService;
    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;
    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private PedigreeRepository pedigreeRepository;
    @Autowired
    private CommonService commonService;
    @Autowired
    private WorkLineRepository workLineRepository;
    @Autowired
    private SaleOrderRepository saleOrderRepository;
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private WorkFlowStepService workFlowStepService;
    @Autowired
    private WorkFlowRepository workFlowRepository;
    @Autowired
    private ProcessTaskFeign processTaskFeign;
    @Autowired
    private BomFeignClient bomFeignClient;
    @Autowired
    private SerialNumberHistoryFeignClient serialNumberHistoryFeignClient;
    @Autowired
    private SubWorkSheetService subWorkSheetService;
    @Autowired
    private IWorkSheetSyncMpsFeignClient[] workSheetSyncMpsFeignClients;
    @Autowired
    private IWsMaterialService[] wsMaterialServices;
    @Autowired
    private IShipmentOrderFeignClient[] shipmentOrderFeignClients;
    @Autowired
    private DictionaryFeignClient dictionaryFeignClient;
    @Autowired
    private IWorkSheetStepStatisticsService[] workSheetStepStatisticsServices;
    @Autowired
    private WorkSheetServiceImpl workSheetServiceImpl;


    /**
     * 新增生产工单信息
     *
     * @param workSheetDto 总工单参数信息
     * @return @return net.airuima.service.procedure.aps.dto.WorkSheetResDTO 正常单与返工单下交返回数据
     * <AUTHOR>
     * @date 2021-01-12
     **/
    @Override
    public WorkSheetResDTO saveInstance(WorkSheetDTO workSheetDto) throws Exception {
        BaseDTO responseResult = workSheetService.validateWorkSheet(workSheetDto.getId(),workSheetDto.getSerialNumber(),workSheetDto.getNumber());
        if (Constants.KO.equals(responseResult.getStatus())) {
            return new WorkSheetResDTO(responseResult);
        }
        workSheetService.validateStepInfo(workSheetDto);
        WorkSheet workSheet = MapperUtils.map(workSheetDto, WorkSheet.class);
        List<BomDTO> bomDtoList = ResponseDataUtils.get(bomFeignClient.findByBomInfoId(workSheetDto.getBomInfoId()));
        //正常单必须要有物料清单
        if(workSheetDto.getCategory() == WsEnum.NORMAL_WS.getCategory() && CollectionUtils.isEmpty(bomDtoList)){
            throw new ResponseException("error.bomNotExist", "物料清单不存在");
        }
        Pedigree pedigree = pedigreeRepository.getReferenceById(workSheetDto.getPedigree().getId());
        WorkFlow workFlow = workFlowRepository.getReferenceById(workSheetDto.getWorkFlow().getId());
        if (workSheetDto.getSaleOrderId() != null) {
            Optional<SaleOrder> saleOrder = saleOrderRepository.findByIdAndDeleted(workSheetDto.getSaleOrderId(), Constants.LONG_ZERO);
            if (saleOrder.isPresent()) {
                saleOrder.get().setProductionQuantity(saleOrder.get().getProductionQuantity() + workSheetDto.getNumber());
                workSheet.setSaleOrder(saleOrder.get());
                saleOrderRepository.save(saleOrder.get());
            }
        }
        //获取产品谱系配置信息来设定工单计划结单日期,没有配置则默认7天
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigreeRepository.getReferenceById(workSheetDto.getPedigree().getId()));
        if (Objects.nonNull(workSheetDto.getIsAutoGenerateSubWs()) && Boolean.TRUE.equals(workSheetDto.getIsAutoGenerateSubWs())) {
            //如果没有找到分单配置 直接拦截
            if (null == pedigreeConfig) {
                throw new ResponseException("error.pedigreeConfigNotExist", "产品谱系分单配置不存在");
            }
            if (Objects.equals(pedigreeConfig.getSplitNumber(), Constants.INT_ZERO)) {
                throw new ResponseException("error.pedigreeConfigSplitNumberZero", "产品谱系分单数量不能为0");
            }
        }
        workSheet.setPedigree(pedigree);
        int plusDays = null != pedigreeConfig ? pedigreeConfig.getPlanFinishDay() : Constants.INT_SEVEN;
        workSheet.setPlanEndDate(workSheetDto.getPlanEndDate() != null ? workSheetDto.getPlanEndDate() : workSheetDto.getPlanStartDate().plusDays(plusDays));
        workSheet.setDeleted(Constants.LONG_ZERO);
        workSheet = workSheetRepository.save(workSheet);
        //保存工单投料单
        wsMaterialServices[0].saveWsMaterial(workSheet, bomDtoList);
        //保存工序快照,并分子工单
        workSheetService.saveWsStepInfo(workSheet, workFlow, pedigree, workSheetDto);
        // 同步工单信息到MPS
        workSheetSyncMpsFeignClients[0].syncMpsWorkSheet(workSheet.getId());
        // 同步工单信息到MPS
        BaseDTO baseDTO = shipmentOrderFeignClients[0].syncShipmentOrder(workSheet);
        if(baseDTO.getStatus().equals(Constants.KO)){
            throw new ResponseException("error.syncShipmentOrderFailed", baseDTO.getMessage());
        }
        return new WorkSheetResDTO(responseResult, workSheet);
    }

    /**
     * 更新工单
     * @param workSheetDto 总工单参数信息
     * <AUTHOR>
     * @date  2023/4/20
     * @return net.airuima.service.procedure.aps.dto.WorkSheetResDTO 正常单与返工单下交返回数据
     */
    @Override
    public WorkSheetResDTO updateInstance(WorkSheetDTO workSheetDto) {
        Optional<WorkSheet> workSheetOptional = this.workSheetRepository.findBySerialNumberAndDeleted(workSheetDto.getSerialNumber(), 0L);
        if((workSheetOptional.isPresent() && workSheetOptional.get().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_FINISH.getCategoryName())
                || (workSheetOptional.isPresent() && workSheetOptional.get().getStatus() == ConstantsEnum.WORK_SHEET_STATIC_HALFWAY.getCategoryName())){
            WorkSheet workSheet = workSheetOptional.get();
            workSheet.setCustom1(workSheetDto.getCustom1());
            workSheet.setCustom2(workSheetDto.getCustom2());
            workSheet.setCustom3(workSheetDto.getCustom3());
            workSheetRepository.save(workSheet);
            return new WorkSheetResDTO(new BaseDTO("OK"),workSheet);
        }
        BaseDTO responseResult = workSheetService.validateWorkSheet(workSheetDto.getId(),workSheetDto.getSerialNumber(),workSheetDto.getNumber());
        if (Constants.KO.equals(responseResult.getStatus())) {
            return new WorkSheetResDTO(responseResult);
        }
        String mode = ResponseDataUtils.get(dictionaryFeignClient.findDictionaryByCode(Constants.KEY_PRODUCTION_MODE));
        boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
        WorkSheet workSheet = workSheetRepository.getReferenceById(workSheetDto.getId());
        boolean numberChanged = workSheetDto.getNumber()!=workSheet.getNumber();
        //原始工单数据
        WorkSheetDTO originWorkSheetDto = MapperUtils.map(workSheet, WorkSheetDTO.class);
        Long subWsProducedNumber = batchWorkDetailRepository.countBySubWorkSheetWorkSheetIdAndDeleted(workSheetDto.getId(),Constants.LONG_ZERO);
        if(Objects.isNull(workSheetDto.getWorkLine()) || Objects.isNull(workSheetDto.getWorkLine().getId())){
            throw new ResponseException("error.workLineNotBeNull", "生产线不可为空");
        }
        if(Objects.nonNull(originWorkSheetDto.getWorkFlow()) && Objects.nonNull(workSheetDto.getWorkFlow()) && !originWorkSheetDto.getWorkFlow().getId().equals(workSheetDto.getWorkFlow().getId()) && Objects.nonNull(subWsProducedNumber) && subWsProducedNumber.intValue()>Constants.INT_ZERO){
            throw new ResponseException("error.stepProducedNotUpdateWorkFlow", "工单已投产，不可修改工艺路线");
        }
        if(Objects.nonNull(originWorkSheetDto.getBomInfoId()) && Objects.nonNull(workSheetDto.getBomInfoId()) && !originWorkSheetDto.getBomInfoId().equals(workSheetDto.getBomInfoId()) && Objects.nonNull(subWsProducedNumber) && subWsProducedNumber.intValue()>Constants.INT_ZERO){
            throw new ResponseException("error.stepProducedNotUpdateBomInfo", "工单已投产，不可修改BOM");
        }
        PedigreeConfig pedigreeConfig = commonService.findPedigreeConfig(pedigreeRepository.getReferenceById(workSheetDto.getPedigree().getId()));
        if (Objects.nonNull(workSheetDto.getIsAutoGenerateSubWs()) && Boolean.TRUE.equals(workSheetDto.getIsAutoGenerateSubWs())) {
            //如果没有找到分单配置 直接拦截
            if (null == pedigreeConfig) {
                throw new ResponseException("error.pedigreeConfigNotExist", "产品谱系分单配置不存在");
            }
            if (Objects.equals(pedigreeConfig.getSplitNumber(), Constants.INT_ZERO)) {
                throw new ResponseException("error.pedigreeConfigSplitNumberZero", "产品谱系分单数量不能为0");
            }
        }
        //更新工单数据
        processWorkSheetInfo(workSheetDto, workSheet,pedigreeConfig);
        //如果taskId为空 或者 工单状态!=-1，则执行修改工艺路线
        if (StringUtils.isBlank(workSheetDto.getTaskId()) || Constants.APPROVE != workSheet.getStatus()) {
            //修改工艺路线（先删除工艺快照，然后添加新的工艺快照）
            if (upDataWorkFlow(workSheetDto, workSheet, originWorkSheetDto)) {
                throw new ResponseException("error.workFlowStepNotExist", "工艺路线工序配置不存在");
            }
        } else if (StringUtils.isNotBlank(workSheetDto.getTaskId()) && Constants.APPROVE == workSheet.getStatus()) {
            //如果taskId不为空 并且 工单状态=-1，则进入BOM审批
            bomApprove(workSheetDto, responseResult, workSheet);
        }
        //更新子工单数据
        if(subWsProductionMode) {
            updateInstanceSubWorkSheet(workSheetDto, workSheet, pedigreeConfig);
        }else {
            workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet,null,Boolean.FALSE);
        }
        //如果数量发生变化则需要更新投料单信息
        if(numberChanged && Objects.nonNull(workSheet.getBomInfoId())){
            List<BomDTO> bomDtoList = ResponseDataUtils.get(bomFeignClient.findByBomInfoId(workSheet.getBomInfoId()));
            //保存工单投料单
            wsMaterialServices[0].saveWsMaterial(workSheet, bomDtoList);
        }

        // 同步工单信息到MPS
        workSheetSyncMpsFeignClients[0].syncMpsWorkSheet(workSheet.getId());
        //更新自定义字段
        workSheet.setCustom1(workSheetDto.getCustom1());
        workSheet.setCustom2(workSheetDto.getCustom2());
        workSheet.setCustom3(workSheetDto.getCustom3());
        workSheetRepository.save(workSheet);
        return new WorkSheetResDTO(responseResult,workSheet);
    }


    /**
     * 更新子工单信息
     * @param workSheetDto 补充数据工单
     * @param workSheet 工单信息
     * @param pedigreeConfig 谱系配置
     */
    public void updateInstanceSubWorkSheet(WorkSheetDTO workSheetDto,WorkSheet workSheet,PedigreeConfig pedigreeConfig){
        Long number = subWorkSheetRepository.statsNumberByWorkSheetIdAndDeleted(workSheetDto.getId(), Constants.LONG_ZERO);
        if (number == null) {
            number = Constants.LONG_ZERO;
        }

        //修改时,是自动分单且子工单投产数小于工单投产数，才会根据规则自动分单
        if (Boolean.TRUE.equals(workSheetDto.getIsAutoGenerateSubWs()) && (number < workSheet.getNumber())) {
            //自动划分子工单
            TransactionUtils.afterCommitAsyncExecute(Executors.newSingleThreadExecutor(),()->{
                subWorkSheetService.autoGenerateSubWorkSheet(workSheet.getId(), Optional.ofNullable(workSheet.getWorkLine()).map(WorkLine::getId).orElse(null), workSheet.getPlanStartDate(),workSheet.getPlanEndDate(),
                        null != pedigreeConfig && pedigreeConfig.getSplitNumber() > Constants.INT_ZERO ? pedigreeConfig.getSplitNumber() : Constants.INT_TWO_HUNDRED, workSheetDto.getIsAutoGenerateSubWs());
                //调用rule服务保存流水号历史明细
                if (StringUtils.isNotBlank(workSheetDto.getSerialNumberConfigCode())) {
                    serialNumberHistoryFeignClient.saveSerialNumberHistory(new SerialNumberHistoryDTO(workSheetDto.getSerialNumberConfigCode(), LocalDate.now(), workSheetDto.getSerialNumber(), workSheet.getOrganizationId().toString()));
                }
            });
        }

        //存在对应的子工单可修改对应额工艺路线以及生产线
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(),Constants.LONG_ZERO);
        if(ValidateUtils.isValid(subWorkSheetList)){
            subWorkSheetList.forEach(subWorkSheet -> {
                //子工单的工艺路线，应和工单一样，但生产线可不同
                subWorkSheet.setWorkFlow(workSheet.getWorkFlow());
                if(null == subWorkSheet.getWorkLine()){
                    subWorkSheet.setWorkLine(workSheet.getWorkLine());
                }
            });
            subWorkSheetRepository.saveAll(subWorkSheetList);
            //初始化更新生产在制看板数据
            workSheetStepStatisticsServices[0].initWorkSheetStepStatisticsInfo(workSheet,subWorkSheetList,Boolean.TRUE);
        }
    }


    /**
     * 处理bom审批
     * @param workSheetDto      总工单DTO
     * @param responseResult    检验返回对象
     * @param workSheet         生产总工单
     * @return net.airuima.service.procedure.aps.dto.WorkSheetResDTO 正常单与返工单下交返回数据
     */
    private WorkSheetResDTO bomApprove(WorkSheetDTO workSheetDto, BaseDTO responseResult, WorkSheet workSheet) {
        //如果taskId不为空 并且 工单状态=-1，则进入BOM审批
        String starter = SecurityUtils.getCurrentUserLogin().orElse(StringUtils.EMPTY);
        FlowableTaskCompleteDTO flowableTaskCompleteDto = new FlowableTaskCompleteDTO().setTaskId(workSheetDto.getTaskId()).setComment(workSheetDto.getNote()).setProcessInstanceId(workSheet.getProcessInstanceId()).setLoginName(starter);
        Map<String, Object> parameter = new HashMap<>();
        parameter.put(FLOWABLE_REAPPLY, Boolean.TRUE);
        flowableTaskCompleteDto.setParameter(parameter);
        if (ValidateUtils.isValid(workSheetDto.getAttachmentList())) {
            flowableTaskCompleteDto.setAttachmentList(MapperUtils.mapAll(workSheetDto.getAttachmentList(), FlowableTaskCompleteDTO.AttachmentInfo.class));
        }
        // Feign调用 Flowable“任务完成处理接口”，进入BOM审批
        ResponseEntity<FlowableResultDTO> taskResponseEntity = processTaskFeign.completeTask(flowableTaskCompleteDto);
        FlowableResultDTO flowableResultDto = taskResponseEntity.getBody();
        if (org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) || flowableResultDto.getStatus().equals(Constants.KO)) {
            throw new ResponseException("error.modifyWorkSheetApplyFailed", org.springframework.util.ObjectUtils.isEmpty(flowableResultDto) ? "调用工作流失败" :flowableResultDto.getMessage());
        }
        return new WorkSheetResDTO(responseResult,workSheet);
    }

    /**
     * 更新工作流
     * @param workSheetDto          总工单DTO
     * @param workSheet             生产总工单
     * @param originWorkSheetDto    原始工单
     * @return boolean失败返回true
     */
    private boolean upDataWorkFlow(WorkSheetDTO workSheetDto, WorkSheet workSheet, WorkSheetDTO originWorkSheetDto) {
        //修改工艺路线（先删除工艺快照，然后添加新的工艺快照）
        if (!workSheetDto.getWorkFlow().equals(originWorkSheetDto.getWorkFlow())) {
            String mode = ResponseDataUtils.get(dictionaryFeignClient.findDictionaryByCode(Constants.KEY_PRODUCTION_MODE));
            boolean subWsProductionMode = StringUtils.isBlank(mode) || Boolean.parseBoolean(mode);
            wsStepRepository.deleteByWorkSheetId(workSheet.getId());
            if (null != workSheetDto.getWorkFlow().getId()) {
                //通过工艺路线获取工序配置
                WorkFlowDTO workFlowDto = workFlowStepService.findByWorkFlowId(workSheetDto.getWorkFlow().getId());
                if (!ValidateUtils.isValid(workFlowDto.getStepDtoList())) {
                    return true;
                }
                WorkFlow workFlow = workFlowRepository.getReferenceById(workSheetDto.getWorkFlow().getId());
                Pedigree pedigree = pedigreeRepository.getReferenceById(workSheetDto.getPedigree().getId());
                //保存工单定制工序
                workSheetService.saveWsSteps(subWsProductionMode,workSheet, workFlow, pedigree, workFlowDto.getStepDtoList());
            }
        }
        return false;
    }

    /**
     * 工单赋值
     * @param workSheetDto 工单dto
     * @param workSheet 工单
     */
    private void processWorkSheetInfo(WorkSheetDTO workSheetDto, WorkSheet workSheet,PedigreeConfig pedigreeConfig) {

        updateWorkSheetInfo(workSheetDto, workSheet, pedigreeConfig);

        updateSaleOrder(workSheetDto, workSheet);

        workSheet.setDeliveryDate(workSheetDto.getDeliveryDate());
        workSheet.setWorkFlow(workSheetDto.getWorkFlow());
        workSheet.setNumber(workSheetDto.getNumber());
        workSheet.setNote(workSheetDto.getNote());
        workSheet.setClientId(workSheetDto.getClientDTO()==null?null: workSheetDto.getClientDTO().getId());
        workSheet.setPriority(workSheetDto.getPriority());
        workSheetService.update(workSheet);
    }

    /**
     * 更新生产工单信息
     * @param  workSheetDto     工单dto
     * @param workSheet         生产总工单
     * @param pedigreeConfig    产品谱系属性
     */
    private void updateWorkSheetInfo(WorkSheetDTO workSheetDto, WorkSheet workSheet, PedigreeConfig pedigreeConfig) {
        //修改计划开始时间（获取产品谱系配置信息来设定工单计划结单日期,没有配置则默认7天）
        if (null != workSheetDto.getPlanStartDate()){
            workSheet.setPlanStartDate(workSheetDto.getPlanStartDate());
        }
        int plusDays = null != pedigreeConfig ? pedigreeConfig.getPlanFinishDay() : Constants.INT_SEVEN;
        workSheet.setPlanEndDate(workSheetDto.getPlanEndDate() != null ? workSheetDto.getPlanEndDate(): workSheetDto.getPlanStartDate().plusDays(plusDays));
        //修改客户&生产线
        if (null != workSheetDto.getClientId()) {
            workSheet.setClientId(workSheetDto.getClientId());
        }
        if (null != workSheetDto.getWorkLine()) {
            workSheet.setWorkLine(workLineRepository.getReferenceById(workSheetDto.getWorkLine().getId()));
        }
    }

    /**
     * 更新销售订单
     * @param workSheetDto 工单dto
     * @param workSheet     生产总工单
     */
    private void updateSaleOrder(WorkSheetDTO workSheetDto, WorkSheet workSheet) {
        if (workSheet.getSaleOrder() != null){
            workSheet.getSaleOrder().setProductionQuantity(workSheet.getSaleOrder().getProductionQuantity() - workSheetDto.getNumber());
            saleOrderRepository.save(workSheet.getSaleOrder());
        }
        if (workSheetDto.getSaleOrderId() != null){
            Optional<SaleOrder> saleOrder = saleOrderRepository.findByIdAndDeleted(workSheetDto.getSaleOrderId(), Constants.LONG_ZERO);
            if (saleOrder.isPresent()){
                saleOrder.get().setProductionQuantity(saleOrder.get().getProductionQuantity() + workSheetDto.getNumber());
                workSheet.setSaleOrder(saleOrder.get());
                saleOrderRepository.save(saleOrder.get());
            }
        }
    }

}
