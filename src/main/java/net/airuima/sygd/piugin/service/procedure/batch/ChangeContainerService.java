package net.airuima.sygd.piugin.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.constant.Constants;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchMethod;
import net.airuima.repository.base.process.StepRepository;
import net.airuima.repository.base.scene.WorkCellRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainerInfo;
import net.airuima.sygd.piugin.repository.procedure.batch.ChangeContainerInfoRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ChangeContainerRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkSheetContainerRepository;
import net.airuima.sygd.piugin.service.procedure.WorkFlowOrderTemplateService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ChangeContainerRequestDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerRemainDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MergeContainerDTO;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转换容器插件Service
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ChangeContainerService extends CommonJpaService<ChangeContainer> {

    private static final String WORK_SHEET_CONTAINER_ENTITY_GRAPH = "changeContainerEntityGraph";

    private final ChangeContainerRepository changeContainerRepository;

    public ChangeContainerService(ChangeContainerRepository changeContainerRepository) {
        this.changeContainerRepository = changeContainerRepository;
    }

    @Autowired
    private ContainerAdaptService containerAdaptService;

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private ContainerAdaptRepository containerAdaptRepository;

    @Autowired
    private WorkCellRepository workCellRepository;

    @Autowired
    private WorkSheetContainerRepository workSheetContainerRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;

    @Autowired
    private ChangeContainerInfoRepository changeContainerInfoRepository;

    private Logger log = LoggerFactory.getLogger(ChangeContainerService.class);


    @Override
    @FetchMethod
    public Page<ChangeContainer> find(Specification<ChangeContainer> spec, Pageable pageable) {
        return changeContainerRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public List<ChangeContainer> find(Specification<ChangeContainer> spec) {
        return changeContainerRepository.findAll(spec, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }

    @Override
    @FetchMethod
    public Page<ChangeContainer> findAll(Pageable pageable) {
        return changeContainerRepository.findAll(pageable, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }

    /**
     * 转换容器
     *
     * @param changeContainerRequestList 转换容器请求
     * @return Boolean 是否转换成功
     */
    public Boolean changeContainer(List<ChangeContainerRequestDTO> changeContainerRequestList) {
        if (CollectionUtils.isEmpty(changeContainerRequestList)) {
            throw new ResponseException("error.ChangeContainerRequestIsNull", "转换容器请求为空");
        }
        changeContainerRequestList.forEach(this::saveChangeContainer);
        return true;
    }

    /**
     * 单个转换容器记录
     *
     * @param changeContainerRequestDTO 转换容器请求
     */
    private void saveChangeContainer(ChangeContainerRequestDTO changeContainerRequestDTO) {
        // 子工单ID
        Long subWorkSheetId = changeContainerRequestDTO.getSubWorkSheetId();
        // 容器ID
        Long containerId = changeContainerRequestDTO.getContainerId();
        // 原容器ID
        Long originContainerId = changeContainerRequestDTO.getOriginContainerId();
        // 数量
        Integer number = Optional.ofNullable(changeContainerRequestDTO.getNumber()).orElse(1120);
        // 工位ID
        Long workCellId = changeContainerRequestDTO.getWorkCellId();
        // 操作员ID
        Long operatorId = changeContainerRequestDTO.getOperatorId();
        // 剩余数量
        Integer remainNumber = Optional.ofNullable(changeContainerRequestDTO.getRemainNumber()).orElse(120);
        // 工序ID
        Long stepId = changeContainerRequestDTO.getStepId();
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            throw new ResponseException("error.SubWorkSheetNotExist", "子工单不存在");
        }
        Optional<Container> containerOptional = containerAdaptRepository.findByIdAndDeleted(containerId, Constants.LONG_ZERO);
        if (containerOptional.isEmpty()) {
            throw new ResponseException("error.ContainerNotExist", "容器不存在");
        }
        Optional<Container> originContainerOptional = containerAdaptRepository.findByIdAndDeleted(originContainerId, Constants.LONG_ZERO);
        if (originContainerOptional.isEmpty()) {
            throw new ResponseException("error.OriginContainerNotExist", "原容器不存在");
        }
        Optional<WorkCell> workCellOptional = workCellRepository.findByIdAndDeleted(workCellId, Constants.LONG_ZERO);
        if (workCellOptional.isEmpty()) {
            throw new ResponseException("error.WorkCellNotExist", "工位不存在");
        }
        // 工序
        Optional<Step> stepOptional = stepRepository.findByIdAndDeleted(stepId, Constants.LONG_ZERO);
        if (stepOptional.isEmpty()) {
            throw new ResponseException("error.StepNotExist", "工序不存在");
        }
        WorkCell workCell = workCellOptional.get();
        Container originContainer = originContainerOptional.get();
        Container container = containerOptional.get();
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        ChangeContainer changeContainer = null;
        //查询删除的容器记录
        Integer maxVersion = Constants.INT_ONE;
        List<ChangeContainer> changeContainerDoneList = changeContainerRepository.findByDeletedChangeHistory(subWorkSheetId, workCellId, containerId, stepId);
        if (!CollectionUtils.isEmpty(changeContainerDoneList)) {
            // 获取数值最大的版本号
            maxVersion = changeContainerDoneList.stream().map(ChangeContainer::getVersion).max(Integer::compareTo).orElse(Constants.INT_ONE);
            // 版本号加1
            maxVersion = maxVersion + 1;
        }
        ChangeContainer queryChangeContainer = changeContainerRepository.findBySubWorkSheetIdAndWorkCellIdAndContainerIdAndStepIdAndDeleted(subWorkSheetId, workCellId, containerId, stepId, Constants.LONG_ZERO);
        if (queryChangeContainer != null) {
            changeContainer = queryChangeContainer;
        } else {
            changeContainer = new ChangeContainer();
        }
        // 记录转容器信息的大小数量 只用记录一次
        Optional<ChangeContainerInfo> changeContainerInfoOptional = changeContainerInfoRepository.findBySubWorkSheetIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
        if (changeContainerInfoOptional.isEmpty()) {
            ChangeContainerInfo changeContainerInfo = new ChangeContainerInfo();
            changeContainerInfo.setSubWorkSheet(subWorkSheet).setWorkSheet(subWorkSheet.getWorkSheet());
            if (container.getCustom2() != null) {
                changeContainerInfo.setBigNumber(Integer.parseInt(container.getCustom2()) + remainNumber)
                        .setRemainNumber(remainNumber)
                        .setSmallNumber(Integer.parseInt(container.getCustom2()));
                changeContainerInfo.setDeleted(Constants.LONG_ZERO);
                changeContainerInfoRepository.save(changeContainerInfo);
            }
        }
        // 版本号
        changeContainer.setVersion(maxVersion);
        changeContainer.setSubWorkSheet(subWorkSheet)
                .setWorkSheet(subWorkSheet.getWorkSheet())
                .setContainer(container)
                .setOriginContainer(originContainer)
                .setNumber(number)
                .setRemainNumber(remainNumber)
                .setOperatorId(operatorId)
                .setStep(stepOptional.get())
                .setWorkCell(workCell);
        // 保存原容器数量
        changeContainer.setCustom1(originContainer.getCustom2());
        // 保存新容器数量
        changeContainer.setCustom2(container.getCustom2());
        changeContainer.setDeleted(Constants.LONG_ZERO);
        changeContainerRepository.save(changeContainer);
        // Custom3记录转容器前的数量 回退需要用到
        originContainer.setCustom3(originContainer.getCustom2());
        // 更新原容器当前数量 custom2为容器当前数量
        originContainer.setCustom2(String.valueOf(remainNumber));
        containerAdaptRepository.save(originContainer);
        // 记录转容器前的数量 回退需要用到
        container.setCustom3(container.getCustom2());
        // 更新新容器当前数量 custom2为容器当前数量
        container.setCustom2(String.valueOf(number));
        containerAdaptRepository.save(container);

    }

    /**
     * 获取工位容器
     *
     * @param id 工位ID
     * @return java.util.List<net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerRemainDTO> 容器信息
     */
    @Transactional(readOnly = true)
    public List<ContainerRemainDTO> getContainerByWorkCell(Long id) {
        List<ChangeContainer> changeContainerList = changeContainerRepository.findAvailableByWorkCellIdAndDeleted(id, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(changeContainerList)) {
            return Lists.newArrayList();
        }
        // 创建日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<ContainerRemainDTO> containerRemainDTOList = Lists.newArrayList();
        for (ChangeContainer changeContainer : changeContainerList) {
            ContainerRemainDTO containerRemainDTO = new ContainerRemainDTO();
            // 转换到北京时区
            ZonedDateTime zonedDateTime = changeContainer.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai"));
            // 格式化日期时间
            String formattedDateTime = zonedDateTime.format(formatter);
            StaffDTO staffDTO = Optional.ofNullable(changeContainer).map(ChangeContainer::getOperatorDto).orElse(null);
            containerRemainDTO.setContainerCode(changeContainer.getOriginContainer().getCode())
                    .setSubWorkSheetSerialNumber(changeContainer.getSubWorkSheet().getSerialNumber())
                    .setWorkSheetSerialNumber(changeContainer.getWorkSheet().getSerialNumber())
                    .setWorkCellName(changeContainer.getWorkCell().getName())
                    .setWorkCellCode(changeContainer.getWorkCell().getCode())
                    .setStaffName(staffDTO != null ? staffDTO.getName() : null)
                    .setStaffCode(staffDTO != null ? staffDTO.getCode() : null)
                    .setNumber(changeContainer.getNumber())
                    .setId(changeContainer.getId())
                    .setStepCode(Optional.of(changeContainer).map(ChangeContainer::getStep).map(Step::getCode).orElse(null))
                    .setStepName(Optional.of(changeContainer).map(ChangeContainer::getStep).map(Step::getName).orElse(null))
                    .setCreatedDate(formattedDateTime)
                    .setRemainNumber(changeContainer.getRemainNumber());
            containerRemainDTOList.add(containerRemainDTO);
        }
        return containerRemainDTOList;
    }

    /**
     * 合并容器
     *
     * @param mergeContainerDTO 合并容器DTO
     * @return java.lang.Boolean  是否合并成功
     */
    public Boolean mergeContainer(MergeContainerDTO mergeContainerDTO) {
        List<Long> idList = mergeContainerDTO.getIdList();
        // 合并后新的容器
        String mergerContainerCode = mergeContainerDTO.getMergerContainerCode();
        if (CollectionUtils.isEmpty(idList)) {
            throw new ResponseException("error.ChoseContainerIsNull", "请选择要合并的容器");
        }
        List<ChangeContainer> changeContainerList = changeContainerRepository.findByIdInAndDeleted(idList, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(changeContainerList)) {
            throw new ResponseException("error.RemainContainerNotExist", "未查到剩余容器");
        }
        // 要同一个工单的容器才能合并
        if (changeContainerList.stream().map(ChangeContainer::getSubWorkSheet).map(SubWorkSheet::getWorkSheet).map(WorkSheet::getId).distinct().count() > 1) {
            throw new ResponseException("error.ContainerNotSameSubWorkSheet", "容器不属于同一个子工单");
        }
        SubWorkSheet subWorkSheet = changeContainerList.get(Constants.INT_ZERO).getSubWorkSheet();
        // 同一个工序才能合并
        if (changeContainerList.stream().map(ChangeContainer::getStep).filter(Objects::nonNull).map(Step::getId).distinct().count() > 1) {
            throw new ResponseException("error.ContainerNotSameWorkProcess", "容器不属于同一个工序");
        }
        Optional<Container> mergerContainerOptional = containerAdaptRepository.findByCodeAndDeleted(mergerContainerCode, Constants.LONG_ZERO);
        Container mergerContainer = null;
        Integer todoMergeNumber = changeContainerList.stream().map(ChangeContainer::getRemainNumber).reduce(0, Integer::sum);
        Integer smallNumber = null;
        if (mergerContainerOptional.isEmpty()) {
            throw new ResponseException("error.MergerContainerNotExist", "合并容器不存在");
        } else {
            mergerContainer = mergerContainerOptional.get();
            //记录合并容器
            Optional<ChangeContainerInfo> changeContainerInfoOptional = changeContainerInfoRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (changeContainerInfoOptional.isPresent()) {
                // 记录合并容器和容器合并前的数量
                ChangeContainerInfo changeContainerInfo = changeContainerInfoOptional.get();
                // 判断合并数
                if (mergerContainer.getCustom2() != null && changeContainerInfo.getSmallNumber() != null) {
                    Integer verifySmallNumber = todoMergeNumber + Integer.parseInt(mergerContainer.getCustom2());
                    if (changeContainerInfo.getSmallNumber() != null) {
                        // verifySmallNumber 能被changeContainerInfo.getSmallNumber()整除
                        if (verifySmallNumber % changeContainerInfo.getSmallNumber() != 0) {
                            throw new ResponseException("error.ContainerMergeNumberNotEqual", "合成容器数量需要凑整" + changeContainerInfo.getSmallNumber());
                        }
                        smallNumber = changeContainerInfo.getSmallNumber();
                    }
                }
                changeContainerInfo.setMergeContainer(mergerContainer)
                        .setLastNumber(ObjectUtils.isEmpty(mergerContainer.getCustom2()) ? 0 : Integer.parseInt(mergerContainer.getCustom2()));
                changeContainerInfoRepository.save(changeContainerInfo);
            }
            if (smallNumber != null) {
                Integer verifySmallNumber = todoMergeNumber + Integer.parseInt(Optional.ofNullable(mergerContainer).map(Container::getCustom2).orElse("0"));
                // 1000以下 刚好 打的数量等于小的数量
                if (verifySmallNumber.equals(smallNumber)) {
                    mergerContainer.setCode(mergerContainerCode)
                            .setName(mergerContainerCode)
                            .setStatus(false)
                            .setDeleted(Constants.LONG_ZERO);
                    // custom3 记录容器被加前的数量
                    mergerContainer.setCustom3(mergerContainer.getCustom2());
                    // 更新新的容器数量
                    if (mergerContainer.getCustom2() != null) {
                        todoMergeNumber += Integer.parseInt(mergerContainer.getCustom2());
                    }
                    // custom2为容器数量  记录容器更新后数量
                    mergerContainer.setCustom2(String.valueOf(todoMergeNumber));
                    containerAdaptRepository.save(mergerContainer);
                    // 更新容器生产详情
                    Optional<ContainerDetail> mergeContainerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(mergerContainer.getId(), Constants.LONG_ZERO);
                    if (mergeContainerDetailOptional.isPresent()) {
                        ContainerDetail containerDetail = mergeContainerDetailOptional.get();
                        // custom3 记录容器被加前的数量
                        containerDetail.setCustom3(String.valueOf(containerDetail.getTransferNumber()));
                        containerDetail.setTransferNumber(todoMergeNumber).setCustom1("merge done");
                        containerDetailRepository.save(containerDetail);
                    }
                }
                // 大于1000则需要进行创建额外的容器
                if (verifySmallNumber > smallNumber) {
                    if (verifySmallNumber % smallNumber != 0) {
                        throw new ResponseException("error.ContainerMergeNumberNotEqual", "合成容器数量需要凑整" + smallNumber + "的倍数");
                    } else {
                        mergerContainer.setCode(mergerContainerCode)
                                .setName(mergerContainerCode)
                                .setStatus(false)
                                .setDeleted(Constants.LONG_ZERO);
                        // custom3 记录容器被加前的数量
                        mergerContainer.setCustom3(mergerContainer.getCustom2());
                        // custom2为容器数量  记录容器更新后数量
                        mergerContainer.setCustom2(String.valueOf(smallNumber));
                        containerAdaptRepository.save(mergerContainer);
                        // 更新容器生产详情
                        ContainerDetail copyContainerDetail = null;
                        Optional<ContainerDetail> mergeContainerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(mergerContainer.getId(), Constants.LONG_ZERO);
                        if (mergeContainerDetailOptional.isPresent()) {
                            ContainerDetail containerDetail = mergeContainerDetailOptional.get();
                            copyContainerDetail = containerDetail;
                            // custom3 记录容器被加前的数量
                            containerDetail.setCustom3(String.valueOf(containerDetail.getTransferNumber()));
                            containerDetail.setTransferNumber(smallNumber).setCustom1("merge done");
                            containerDetailRepository.save(containerDetail);
                        }
                        String mergeContainerCode = mergerContainer.getCode();
                        // 截取第二个-的字符串
                        int firstDashIndex = mergeContainerCode.indexOf("-");
                        int secondDashIndex = mergeContainerCode.indexOf("-", firstDashIndex + 1);
                        String beforeSecondDash = secondDashIndex != -1 ? mergeContainerCode.substring(0, secondDashIndex) : mergeContainerCode;
                        String afterSecondDash = secondDashIndex != -1 ? mergeContainerCode.substring(secondDashIndex + 1) : "";
                        if (!ObjectUtils.isEmpty(afterSecondDash)) {
                            String format = "%0" + afterSecondDash.length() + "d";
                            // 超过小的容器数量另外生成容器和容器详情
                            Integer overNumber = verifySmallNumber - smallNumber;
                            Integer newContainerCount = overNumber / smallNumber;
                            Integer latestSerialNumber = Integer.valueOf(afterSecondDash);
                            log.info("开始增量容器新增," + latestSerialNumber);
                            for (int i = 0; i < newContainerCount; i++) {
                                String currentSerialNumber = String.format(format, latestSerialNumber + i + 1);
                                Container newContainer = new Container();
                                newContainer.setCode(beforeSecondDash + "-" + currentSerialNumber)
                                        .setName(beforeSecondDash + "-" + currentSerialNumber)
                                        .setStatus(false)
                                        .setDeleted(Constants.LONG_ZERO);
                                newContainer.setCustom2(String.valueOf(smallNumber));
                                containerAdaptRepository.save(newContainer);
                                // 生成容器详情
                                ContainerDetail newContainerDetail = new ContainerDetail();
                                newContainerDetail.setContainer(newContainer)
                                        .setContainerCode(newContainer.getCode())
                                        .setInputNumber(smallNumber)
                                        .setQualifiedNumber(smallNumber)
                                        .setTransferNumber(smallNumber)
                                        .setPreContainerCodeList(Optional.ofNullable(copyContainerDetail).map(ContainerDetail::getPreContainerCodeList).orElse(null))
                                        .setStatus(Constants.INT_ONE)
                                        .setStaffId(Optional.ofNullable(copyContainerDetail).map(ContainerDetail::getStaffId).orElse(null))
                                        .setBatchWorkDetail(Optional.ofNullable(copyContainerDetail).map(ContainerDetail::getBatchWorkDetail).orElse(null));
                                newContainerDetail.setCustom1("merge done" + i + 1);
                                containerDetailRepository.save(newContainerDetail);

                            }
                        }

                    }
                }


            }
        }
        // 设置旧的容器数量为0
        changeContainerList.forEach(changeContainer -> {
            Container originContainer = changeContainer.getOriginContainer();
            // custom2当前容器数量设置为0
            originContainer.setCustom2(String.valueOf(Constants.INT_ZERO));
            containerAdaptRepository.save(originContainer);
            // 更旧的的容器生成详情流转数量为0
            Optional<ContainerDetail> containerDetailOptional = containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(originContainer.getId(), Constants.LONG_ZERO);
            if (containerDetailOptional.isPresent()) {
                ContainerDetail containerDetail = containerDetailOptional.get();
                // custom3记录合并前的数量
                containerDetail.setCustom3(String.valueOf(containerDetail.getTransferNumber()));
                // 容器生产详情 当前流转数为0
                containerDetail.setTransferNumber(Constants.INT_ZERO).setCustom1("merge item");
                containerDetailRepository.save(containerDetail);
            }
        });
        //清理旧的转容器记录
        changeContainerRepository.logicDelete(changeContainerList);
        return true;
    }


    /**
     * 根据可用的待合并容器信息
     *
     * @param stepId         工位id
     * @param subWorksheetId 子工单id
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < ContainerRemainDTO>>> 容器信息
     */
    public List<ContainerRemainDTO> getMergeContainer(Long stepId, Long subWorksheetId) {
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(subWorksheetId, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            return Lists.newArrayList();
        }
        WorkSheet workSheet = subWorkSheetOptional.get().getWorkSheet();
        List<ChangeContainer> changeContainerList = changeContainerRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), stepId, Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(changeContainerList)) {
            return Lists.newArrayList();
        }
        // 创建日期时间格式器
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        List<ContainerRemainDTO> containerRemainDTOList = Lists.newArrayList();
        for (ChangeContainer changeContainer : changeContainerList) {
            ContainerRemainDTO containerRemainDTO = new ContainerRemainDTO();
            // 转换到北京时区
            ZonedDateTime zonedDateTime = changeContainer.getCreatedDate().atZone(ZoneId.of("Asia/Shanghai"));
            // 格式化日期时间
            String formattedDateTime = zonedDateTime.format(formatter);
            StaffDTO staffDTO = Optional.of(changeContainer).map(ChangeContainer::getOperatorDto).orElse(null);
            Integer number = changeContainer.getNumber();
            // 获取待合并容器时更新容器数量
            Container originContainer = changeContainer.getOriginContainer();
            // 查询原容器流转数量
            int remainNumber = changeContainer.getRemainNumber();
            if (remainNumber > Constants.LONG_ZERO) {
                containerRemainDTO.setContainerCode(originContainer.getCode())
                        .setSubWorkSheetSerialNumber(changeContainer.getSubWorkSheet().getSerialNumber())
                        .setWorkSheetSerialNumber(changeContainer.getWorkSheet().getSerialNumber())
                        .setWorkCellName(changeContainer.getWorkCell().getName())
                        .setWorkCellCode(changeContainer.getWorkCell().getCode())
                        .setStaffName(staffDTO != null ? staffDTO.getName() : null)
                        .setStaffCode(staffDTO != null ? staffDTO.getCode() : null)
                        .setNumber(number)
                        .setId(changeContainer.getId())
                        .setStepCode(Optional.of(changeContainer).map(ChangeContainer::getStep).map(Step::getCode).orElse(null))
                        .setStepName(Optional.of(changeContainer).map(ChangeContainer::getStep).map(Step::getName).orElse(null))
                        .setCreatedDate(formattedDateTime)
                        .setRemainNumber(remainNumber);
                containerRemainDTOList.add(containerRemainDTO);
            }
        }
        return containerRemainDTOList;
    }
}
