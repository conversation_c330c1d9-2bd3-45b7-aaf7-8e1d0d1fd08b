package net.airuima.sygd.piugin.service.procedure.batch;

import com.cosium.spring.data.jpa.entity.graph.domain2.NamedEntityGraph;
import net.airuima.service.CommonJpaService;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkSheetContainerRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单容器关联Service
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkSheetContainerService extends CommonJpaService<WorkSheetContainer> {

    private static final String WORK_SHEET_CONTAINER_ENTITY_GRAPH = "workSheetContainerEntityGraph";

    private final WorkSheetContainerRepository workSheetContainerRepository;

    public WorkSheetContainerService(WorkSheetContainerRepository workSheetContainerRepository) {
        this.workSheetContainerRepository = workSheetContainerRepository;
    }

    @Override
    public Page<WorkSheetContainer> find(Specification<WorkSheetContainer> spec, Pageable pageable) {
        return workSheetContainerRepository.findAll(spec, pageable, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }

    @Override
    public List<WorkSheetContainer> find(Specification<WorkSheetContainer> spec) {
        return workSheetContainerRepository.findAll(spec, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }

    @Override
    public Page<WorkSheetContainer> findAll(Pageable pageable) {
        return workSheetContainerRepository.findAll(pageable, new NamedEntityGraph(WORK_SHEET_CONTAINER_ENTITY_GRAPH));
    }



}
