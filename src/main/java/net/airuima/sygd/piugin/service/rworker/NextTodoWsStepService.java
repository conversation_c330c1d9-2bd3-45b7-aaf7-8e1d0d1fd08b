package net.airuima.sygd.piugin.service.rworker;

import net.airuima.client.feign.rmps.api.IPackageRelationFeignClient;
import net.airuima.constant.Constants;
import net.airuima.constant.ConstantsEnum;
import net.airuima.constant.SnWorkStatusEnum;
import net.airuima.constant.WsEnum;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlowStep;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.domain.procedure.batch.WsStep;
import net.airuima.domain.procedure.single.SnWorkStatus;
import net.airuima.domain.procedure.single.WorkSheetSn;
import net.airuima.dto.process.StepDTO;
import net.airuima.repository.base.process.StepRepository;
import net.airuima.repository.base.process.WorkFlowStepRepository;
import net.airuima.repository.base.scene.WorkCellStaffRepository;
import net.airuima.repository.base.scene.WorkCellStepRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.BatchWorkDetailRepository;
import net.airuima.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.repository.procedure.batch.ContainerRepository;
import net.airuima.repository.procedure.batch.WsStepRepository;
import net.airuima.repository.procedure.single.SnWorkStatusRepository;
import net.airuima.repository.procedure.single.WorkSheetSnRepository;
import net.airuima.service.common.CommonService;
import net.airuima.service.rworker.process.IContainerProcessRequestService;
import net.airuima.sygd.piugin.web.rest.rworker.dto.NextTodoWsStepDTO;
import net.airuima.util.BeanUtil;
import net.airuima.util.ResponseException;
import net.airuima.util.ValidateUtils;
import net.airuima.web.rest.rworker.process.dto.RworkerBatchToDoStepGetDTO;
import net.airuima.web.rest.rworker.process.dto.RworkerContainerToDoStepGetDTO;
import net.airuima.web.rest.rworker.process.dto.RworkerSnToDoStepGetDTO;
import net.airuima.web.rest.rworker.process.dto.RworkerStepProcessBaseDTO;
import net.airuima.web.rest.rworker.process.dto.general.WsStepGetInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 下一个待做工序步骤服务类
 * 负责根据不同的请求类型（工单、容器、SN）查找下一个待执行的工序步骤
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class NextTodoWsStepService {

    // 常量定义
    private static final String ERR_MSG = "当前工位无工序可生产";
    private static final String NOT_EXIST_TODO_STEP = "notExistTodoStep";

    // 工单类型常量
    private static final int CATEGORY_WORKSHEET = 0;
    private static final int CATEGORY_CONTAINER = 1;
    private static final int CATEGORY_SN = 2;

    // Repository依赖注入
    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;

    @Autowired
    private WsStepRepository wsStepRepository;

    @Autowired
    private CommonService commonService;

    @Autowired
    private WorkCellStaffRepository workCellStaffRepository;

    @Autowired
    private WorkCellStepRepository workCellStepRepository;

    @Autowired
    private SnWorkStatusRepository snWorkStatusRepository;

    @Autowired
    private ContainerRepository containerRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private WorkSheetSnRepository workSheetSnRepository;

    @Autowired
    private IPackageRelationFeignClient[] packageRelationFeignClients;

    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;

    public Object findNextTodoWsStep(NextTodoWsStepDTO nextTodoWsStepDTO) {
        // 工单
        if (nextTodoWsStepDTO.getCategory() == 0) {
            return this.findSubWsNextTodoStep(nextTodoWsStepDTO);
        }
        // 容器
        if (nextTodoWsStepDTO.getCategory() == 1) {
            return this.findContainerNextTodoStep(nextTodoWsStepDTO);
        }
        // sn
        if (nextTodoWsStepDTO.getCategory() == 2) {
            return this.findSnNextTodoStep(nextTodoWsStepDTO);
        }
        return null;
    }

    private Object findSnNextTodoStep(NextTodoWsStepDTO nextTodoWsStepDTO) {
        SnWorkStatus snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(nextTodoWsStepDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        //若通过SN查询不到SN生产状态则调用YSN查询生产状态
        if (Objects.isNull(snWorkStatus)) {
            snWorkStatus = snWorkStatusRepository.findByYsnAndDeleted(nextTodoWsStepDTO.getSn(), Constants.LONG_ZERO).orElse(null);
        }
        //工单SN关联对象
        WorkSheetSn workSheetSn = null;
        //内部SN对应的YSN
        String ysn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getYsn() : null;
        //生产流转SN
        String transferSn = Objects.nonNull(snWorkStatus) ? snWorkStatus.getSn() : nextTodoWsStepDTO.getSn();
        //如果为空可能是YSN，则需要获取RMPS的流转SN
        if (Objects.isNull(snWorkStatus)) {
            workSheetSn = workSheetSnRepository.findBySnAndDeleted(transferSn, Constants.LONG_ZERO).orElse(null);
            if (Objects.isNull(workSheetSn)) {
                String sn = packageRelationFeignClients[0].findProcessSn(nextTodoWsStepDTO.getSn(), null);
                if (StringUtils.isNotBlank(sn)) {
                    ysn = nextTodoWsStepDTO.getSn();
                    transferSn = sn;
                    snWorkStatus = snWorkStatusRepository.findBySnAndDeleted(sn, Constants.LONG_ZERO).orElse(null);
                }
            }
        }
        if (null == snWorkStatus && Objects.isNull(workSheetSn)) {
            throw new ResponseException("error.snIsNotExist", "SN在系统中不存在!");
        }
        SubWorkSheet subWorkSheet = workSheetSn.getSubWorkSheet();
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (CollectionUtils.isEmpty(wsStepList)) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }
        WsStep nextTodoWsStep = null;
        List<WsStep> firstReworkWsStepList = null;
        WsStep wsStep1 = new WsStep();
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        //若SN处于投产中状态则认为SN正在正常单中投产
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.PUT_INTO_PRODUCTION.getStatus()) {
            if (subWorkSheet.getWorkSheet().getCategory() != WsEnum.NORMAL_WS.getCategory() && !snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(subWorkSheet.getId())) {
                firstReworkWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).toList();
            }
            if (!CollectionUtils.isEmpty(firstReworkWsStepList)) {
                if (nextTodoWsStepDTO.getWorkCellId() == null) {
                    nextTodoWsStep = firstReworkWsStepList.stream().findFirst().orElse(null);
                    if (Objects.isNull(nextTodoWsStep)) {
                        throw new ResponseException("error.notExistTodoStep", "SN不存在待做工序");
                    }
                } else {
                    //获取工位可生产的工序列表
                    List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                    List<Step> finalWorkCellStepList = workCellStepList;
                    nextTodoWsStep = firstReworkWsStepList.stream().filter(wsStep -> finalWorkCellStepList.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
                    if (nextTodoWsStep == null) {
                        // 获取员工工位
                        List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                        WorkCell messgesWorkCell = null;
                        for (WorkCell cell : workCellList) {
                            workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                            List<Step> finalWorkCellStepList1 = workCellStepList;
                            nextTodoWsStep = firstReworkWsStepList.stream().filter(wsStep -> finalWorkCellStepList1.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
                            if (nextTodoWsStep != null) {
                                messgesWorkCell = cell;
                                break;
                            }
                        }
                        if (nextTodoWsStep == null) {
                            throw new ResponseException("error.notExistTodoStep", "SN不存在待做工序");
                        } else {
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextTodoWsStep.getStep().getName()
                                    + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                        }
                    }
                }
            } else {
                //非返工单则按照SN最新完成SN工作详情以及结合工单工艺快照来判断下个待做工序信息
                SnWorkStatus finalSnWorkStatus = snWorkStatus;
                WsStep latestWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(finalSnWorkStatus.getLatestSnWorkDetail().getStep().getId()))
                        .findFirst().orElseThrow(() -> new ResponseException("error.latestWsStepNotExist", "最新完成的工序在工单工艺快照中不存在"));
                WsStep nextFirstWsStep = wsStepList.stream().filter(wsStep -> latestWsStep.getAfterStepId().contains(wsStep.getStep().getId().toString())).findFirst().orElse(null);
                if (nextTodoWsStepDTO.getWorkCellId() == null) {
                    nextTodoWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(nextFirstWsStep.getStep().getId())).findFirst().orElse(null);
                    if (Objects.isNull(nextTodoWsStep)) {
                        throw new ResponseException("error.notExistTodoStep", "SN不存在待做工序");
                    }
                } else {
                    //获取工位可生产的工序列表
                    List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                    Step nextTodoStep = workCellStepList.stream().filter(step -> latestWsStep.getAfterStepId().contains(step.getId().toString())).findFirst().orElse(null);
                    if (nextTodoStep == null) {
                        // 获取员工工位
                        List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                        WorkCell messgesWorkCell = null;
                        for (WorkCell cell : workCellList) {
                            workCellStepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                            nextTodoStep = workCellStepList.stream().filter(step -> latestWsStep.getAfterStepId().contains(step.getId().toString())).findFirst().orElse(null);
                            if (nextTodoStep != null) {
                                messgesWorkCell = cell;
                                break;
                            }
                        }
                        if (nextTodoStep == null) {
                            throw new ResponseException("error.notExistTodoStep", "SN不存在待做工序");
                        } else {
                            Step finalNextTodoStep = nextTodoStep;
                            nextTodoWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(finalNextTodoStep.getId())).findFirst().orElse(null);
                            if (Objects.isNull(nextTodoWsStep)) {
                                throw new ResponseException("error.notExistTodoStep", "SN不存在待做工序");
                            }
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextFirstWsStep.getStep().getName()
                                    + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                        }
                    }

                }
            }
        }
        //若SN处于返修中状态则认为SN在返工单、返修单中返修或者单支在线直接返修
        if (Objects.nonNull(snWorkStatus) && snWorkStatus.getStatus() == SnWorkStatusEnum.IN_THE_REPAIR.getStatus()) {
            List<WorkFlowStep> workFlowStepList = null;
            //如果SN生产状态的工单为正常单时则按照单支直接在线反工模式进行获取下个待做工序
            if (snWorkStatus.getReworkTime() > Constants.INT_ZERO && snWorkStatus.getLatestSnWorkDetail().getSubWorkSheet().getId().equals(snWorkStatus.getSubWorkSheet().getId())) {
                workFlowStepList = workFlowStepRepository.findStepByWorkFlowIdAndDeleted(snWorkStatus.getWorkFlow().getId(), Constants.LONG_ZERO);
            }
            //如果最新的SN工作详情返修次数标志和SN生产状态的不通，则返修工艺路线的第一个工序就是当前待做工序
            if (snWorkStatus.getLatestSnWorkDetail().getReworkTime() != snWorkStatus.getReworkTime()) {
                List<WorkFlowStep> firstWorkFlowStepList = workFlowStepList.stream().filter(workFlowStep -> StringUtils.isBlank(workFlowStep.getPreStepId())).toList();
                if (nextTodoWsStepDTO.getWorkCellId() == null) {
                    WorkFlowStep nextTodoWorkFlowStep = firstWorkFlowStepList.get(0);
                    if (null == nextTodoWorkFlowStep) {
                        throw new ResponseException("error.notExistTodoStep", "当前SN无待做工序!");
                    }
                    rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
                    wsStep1.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
                } else {
                    //获取工位可生产的工序列表
                    List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                    List<Step> finalWorkCellStepList = workCellStepList;
                    WorkFlowStep nextTodoWorkFlowStep = firstWorkFlowStepList.stream().filter(workFlowStep -> finalWorkCellStepList.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
                    if (nextTodoWorkFlowStep == null) {
                        // 获取员工工位
                        List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                        WorkCell messgesWorkCell = null;
                        for (WorkCell cell : workCellList) {
                            workCellStepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                            List<Step> finalWorkCellStepList1 = workCellStepList;
                            nextTodoWorkFlowStep = firstWorkFlowStepList.stream().filter(workFlowStep -> finalWorkCellStepList1.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
                            if (nextTodoWorkFlowStep != null) {
                                messgesWorkCell = cell;
                                break;
                            }
                        }
                        if (null == nextTodoWorkFlowStep) {
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstWorkFlowStepList.get(Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
                        } else {
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstWorkFlowStepList.get(Constants.INT_ZERO).getStep().getName()
                                    + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                        }
                    }
                    rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
                    wsStep1.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
                }
            } else {
                SnWorkStatus finalSnWorkStatus1 = snWorkStatus;
                WorkFlowStep latestWorkFlowStep = workFlowStepList.stream().filter(workFlowStep -> workFlowStep.getStep().getId().equals(finalSnWorkStatus1.getLatestSnWorkDetail().getStep().getId())).findFirst().orElse(null);
                if (null == latestWorkFlowStep || StringUtils.isBlank(latestWorkFlowStep.getAfterStepId())) {
                    throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
                }
                List<WorkFlowStep> nextTodoWorkFlowStepList = workFlowStepList.stream().filter(workFlowStep -> latestWorkFlowStep.getAfterStepId().contains(workFlowStep.getStep().getId().toString())).toList();
                if (CollectionUtils.isEmpty(nextTodoWorkFlowStepList)) {
                    throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
                }
                if (nextTodoWsStepDTO.getWorkCellId() == null) {
                    WorkFlowStep nextTodoWorkFlowStep = nextTodoWorkFlowStepList.get(Constants.INT_ZERO);
                    if (null == nextTodoWorkFlowStep) {
                        throw new ResponseException(NOT_EXIST_TODO_STEP, ERR_MSG);
                    }
                    rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
                    wsStep1.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
                } else {
                    //获取工位可生产的工序列表
                    List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                    List<Step> finalWorkCellStepList = workCellStepList;
                    WorkFlowStep nextTodoWorkFlowStep = nextTodoWorkFlowStepList.stream().filter(workFlowStep -> finalWorkCellStepList.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
                    if (nextTodoWorkFlowStep == null) {
                        // 获取员工工位
                        List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                        WorkCell messgesWorkCell = null;
                        for (WorkCell cell : workCellList) {
                            workCellStepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                            List<Step> finalWorkCellStepList1 = workCellStepList;
                            nextTodoWorkFlowStep = nextTodoWorkFlowStepList.stream().filter(workFlowStep -> finalWorkCellStepList1.stream().map(Step::getId).toList().contains(workFlowStep.getStep().getId())).findFirst().orElse(null);
                            if (nextTodoWorkFlowStep != null) {
                                messgesWorkCell = cell;
                                break;
                            }
                        }
                        if (nextTodoWorkFlowStep == null) {
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextTodoWorkFlowStepList.get(Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
                        } else {
                            throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + nextTodoWorkFlowStepList.get(Constants.INT_ZERO).getStep().getName()
                                    + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                        }
                    }
                    rworkerStepProcessBaseDTO.setStep(nextTodoWorkFlowStep.getStep());
                    wsStep1.setPreStepId(nextTodoWorkFlowStep.getPreStepId()).setAfterStepId(nextTodoWorkFlowStep.getAfterStepId());
                }
            }
            StepDTO stepDTO = commonService.findPedigreeStepConfig(subWorkSheet.getWorkSheet().getClientId(), subWorkSheet.getWorkSheet().getPedigree(), snWorkStatus.getWorkFlow(), rworkerStepProcessBaseDTO.getStep());
            if (null == stepDTO) {
                throw new ResponseException("error.stepConfigNotExist", "工序(" + rworkerStepProcessBaseDTO.getStep().getCode() + ")请求模式配置不存在");
            }
            if (stepDTO.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
                throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
            }
            if (stepDTO.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
                throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
            }
            wsStep1.setStep(rworkerStepProcessBaseDTO.getStep())
                    .setSubWorkSheet(subWorkSheet)
                    .setRequestMode(stepDTO.getRequestMode())
                    .setCategory(stepDTO.getCategory())
                    .setIsControlMaterial(stepDTO.getIsControlMaterial())
                    .setIsBindContainer(stepDTO.getIsBindContainer())
                    .setControlMode(stepDTO.getControlMode())
                    .setInputRate(Constants.INT_ONE);
            rworkerStepProcessBaseDTO.setWsStep(wsStep1).setWorkFlow(snWorkStatus.getWorkFlow());
            List<WsStepGetInfo> wsStepInfoList = Lists.newArrayList();
            SnWorkStatus finalSnWorkStatus2 = snWorkStatus;
            workFlowStepList.forEach(workFlowStep -> {
                StepDTO stepConfigDTO = commonService.findPedigreeStepConfig(subWorkSheet.getWorkSheet().getClientId(), subWorkSheet.getWorkSheet().getPedigree(), finalSnWorkStatus2.getWorkFlow(), workFlowStep.getStep());
                if (null == stepConfigDTO) {
                    throw new ResponseException("error.stepConfigNotExist", "工序(" + workFlowStep.getStep().getCode() + ")请求模式配置不存在");
                }
                wsStepInfoList.add(new WsStepGetInfo(workFlowStep, stepConfigDTO));
            });
            RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO(wsStep1);
            rworkerSnToDoStepGetDTO.setWsStepInfoList(wsStepInfoList);
            return rworkerSnToDoStepGetDTO;
        }
        //如果SN生产状态为空且工单关联SN不为空则认为SN在做第一个工序
        if (Objects.isNull(snWorkStatus)) {
            List<WsStep> firstOneWsStepList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).toList();
            if (nextTodoWsStepDTO.getWorkCellId() == null) {
                nextTodoWsStep = firstOneWsStepList.get(Constants.INT_ZERO);
                if (Objects.isNull(nextTodoWsStep)) {
                    throw new ResponseException("error.notExistTodoStep", "当前SN无待做工序!");
                }
            } else {
                //获取工位可生产的工序列表
                List<Step> workCellStepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
                List<Step> finalWorkCellStepList = workCellStepList;
                nextTodoWsStep = firstOneWsStepList.stream().filter(wsStep -> finalWorkCellStepList.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
                if (nextTodoWsStep == null) {
                    // 获取员工工位
                    List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                    WorkCell messgesWorkCell = null;
                    for (WorkCell cell : workCellList) {
                        workCellStepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                        List<Step> finalWorkCellStepList1 = workCellStepList;
                        nextTodoWsStep = firstOneWsStepList.stream().filter(wsStep -> finalWorkCellStepList1.stream().map(Step::getId).toList().contains(wsStep.getStep().getId())).findFirst().orElse(null);
                        if (nextTodoWsStep != null) {
                            messgesWorkCell = cell;
                            break;
                        }
                    }
                    if (nextTodoWsStep == null) {
                        throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstOneWsStepList.get(Constants.INT_ZERO).getStep().getName() + ",当前员工没有工位生产该工序");
                    } else {
                        throw new ResponseException("error.notExistTodoStep", "SN待做工序为【" + firstOneWsStepList.get(Constants.INT_ZERO).getStep().getName()
                                + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                    }
                }
            }
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        RworkerSnToDoStepGetDTO rworkerSnToDoStepGetDTO = new RworkerSnToDoStepGetDTO(nextTodoWsStep);
        rworkerSnToDoStepGetDTO.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
        return rworkerSnToDoStepGetDTO;
    }

    private RworkerContainerToDoStepGetDTO findContainerNextTodoStep(NextTodoWsStepDTO nextTodoWsStepDTO) {
        Container container = containerRepository.findByCodeAndDeleted(nextTodoWsStepDTO.getRequestContainerCode(), Constants.LONG_ZERO).orElse(null);
        if (null == container) {
            throw new ResponseException("requestContainerIsNotBinding", "请求工序生产的容器中存在尚未绑定的容器");
        }
        ContainerDetail containerDetail = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeleted(container.getCode(), Constants.INT_ONE, Constants.LONG_ZERO);
        SubWorkSheet subWorkSheet;
        RworkerStepProcessBaseDTO rworkerStepProcessBaseDTO = new RworkerStepProcessBaseDTO();
        if (null == containerDetail) {
            //验证容器是否处于维修分析完成
            BeanUtil.getHighestPrecedenceBean(IContainerProcessRequestService.class).validateRequestContainerMaintain(true, Boolean.FALSE, container.getId(), rworkerStepProcessBaseDTO);
            subWorkSheet = rworkerStepProcessBaseDTO.getSubWorkSheet();
        } else {
            subWorkSheet = containerDetail.getBatchWorkDetail().getSubWorkSheet();
        }
        List<WsStep> wsStepList = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsStepList)) {
            wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        if (null == wsStepList) {
            throw new ResponseException("error.wsStepNotExist", "工单工艺快照不存在");
        }
        // 默认待投产工序列表为工单的第一个工序
        List<Long> nextToDoStepIdList = wsStepList.stream().filter(wsStep -> StringUtils.isBlank(wsStep.getPreStepId())).map(wsStep -> wsStep.getStep().getId()).toList();
        // 若当前容器处于绑定状态则根据容器最新完成的工序及工单工序快照获取下个待投产工序列表
        if (null != containerDetail) {
            WsStep latestFinishedWsStep = wsStepList.stream().filter(wsStep -> containerDetail.getBatchWorkDetail().getStep().getId().equals(wsStep.getStep().getId())).findFirst().orElse(null);
            if (null == latestFinishedWsStep || StringUtils.isBlank(latestFinishedWsStep.getAfterStepId())) {
                throw new ResponseException("error.afterStepNotExist", "请求容器不存在后置待生产工序");
            }
            nextToDoStepIdList = Arrays.stream(latestFinishedWsStep.getAfterStepId().split(Constants.STR_COMMA)).map(Long::parseLong).toList();
        }
        List<Long> finalNextToDoStepIdList = nextToDoStepIdList;
        Step nextToDoStep = null;
        if (nextTodoWsStepDTO.getWorkCellId() == null) {
            nextToDoStep = Objects.requireNonNull(finalNextToDoStepIdList.stream().findFirst().map(aLong -> stepRepository.findById(aLong)).orElse(null)).orElse(null);
            if (nextToDoStep == null) {
                throw new ResponseException("error.notExistTodoStep", "当前容器无工序可生产");
            }
        } else {
            //获取工位可生产的工序列表
            List<Step> stepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId(), Constants.LONG_ZERO);
            nextToDoStep = stepList.stream().filter(step -> finalNextToDoStepIdList.contains(step.getId())).findFirst().orElse(null);
            if (nextToDoStep == null) {
                // 获取员工工位
                List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                WorkCell messgesWorkCell = null;
                for (WorkCell cell : workCellList) {
                    stepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                    nextToDoStep = stepList.stream().filter(step -> finalNextToDoStepIdList.contains(step.getId())).findFirst().orElse(null);
                    if (nextToDoStep != null) {
                        messgesWorkCell = cell;
                        break;
                    }
                }
                if (nextToDoStep == null) {
                    if (!CollectionUtils.isEmpty(finalNextToDoStepIdList)) {
                        Step step = stepRepository.getReferenceById(finalNextToDoStepIdList.get(Constants.INT_ZERO));
                        throw new ResponseException("error.notExistTodoStep", "容器待做工序为【" + step.getName() + ",当前工位不可生产该工序");
                    }
                } else {
                    if (!CollectionUtils.isEmpty(finalNextToDoStepIdList)) {
                        Step step = stepRepository.getReferenceById(finalNextToDoStepIdList.get(Constants.INT_ZERO));
                        throw new ResponseException("error.notExistTodoStep", "容器待做工序为【" + step.getName() + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                    }
                }
                throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
            }
        }
        Step finalNextToDoStep = nextToDoStep;
        WsStep nextToDoWsStep = wsStepList.stream().filter(wsStep -> wsStep.getStep().getId().equals(finalNextToDoStep.getId())).findFirst().orElse(new WsStep());
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.WORK_SHEET_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.workSheetRequestModeError", "请扫描工单进行请求待做工序");
        }
        if (nextToDoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        RworkerContainerToDoStepGetDTO containerNextToDoStepInfo = new RworkerContainerToDoStepGetDTO(nextToDoWsStep);
        containerNextToDoStepInfo.setWsStepInfoList(wsStepList.stream().map(WsStepGetInfo::new).toList());
        return containerNextToDoStepInfo;
    }

    private RworkerBatchToDoStepGetDTO findSubWsNextTodoStep(NextTodoWsStepDTO nextTodoWsStepDTO) {
        SubWorkSheet subWorkSheet = subWorkSheetRepository.getReferenceById(nextTodoWsStepDTO.getProductWorkSheetId());
        //获取当前子工单批量详情列表
        List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        //优先获取子工单工艺快照，若不存在则继续获取工单工艺快照
        List<WsStep> wsSteps = wsStepRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsSteps)) {
            wsSteps = wsStepRepository.findByWorkSheetIdAndDeleted(subWorkSheet.getWorkSheet().getId(), Constants.LONG_ZERO);
        }
        WsStep nextTodoWsStep = null;
        if (nextTodoWsStepDTO.getWorkCellId() == null) {
            nextTodoWsStep = findNextTodoWsStep(wsSteps, null, batchWorkDetailList);
            if (nextTodoWsStep == null) {
                throw new ResponseException("error.notExistTodoStep", "当前工单无工序可生产");
            }
        } else {
            //获取工位绑定的工序列表
            List<Step> stepList = workCellStepRepository.findByWorkCellId(nextTodoWsStepDTO.getWorkCellId());
            nextTodoWsStep = findNextTodoWsStep(wsSteps, stepList, batchWorkDetailList);
            if (nextTodoWsStep == null) {
                // 获取员工工位
                List<WorkCell> workCellList = workCellStaffRepository.findWorkCellByStaffId(nextTodoWsStepDTO.getStaffId());
                WorkCell messgesWorkCell = null;
                for (WorkCell cell : workCellList) {
                    stepList = workCellStepRepository.findByWorkCellId(cell.getId(), Constants.LONG_ZERO);
                    nextTodoWsStep = findNextTodoWsStep(wsSteps, stepList, batchWorkDetailList);
                    if (nextTodoWsStep != null) {
                        messgesWorkCell = cell;
                        break;
                    }
                }
                List<List<WsStep>> lists = commonService.dealWsStep(wsSteps);
                if (nextTodoWsStep == null) {
                    if (CollectionUtils.isEmpty(batchWorkDetailList)) {
                        throw new ResponseException("error.notExistTodoStep", "工单待做工序为【" + lists.get(Constants.INT_ZERO).get(Constants.INT_ZERO).getStep().getName() + ",当前工位不可生产该工序");
                    }
                    for (List<WsStep> wsStepList : lists) {
                        for (WsStep wsStep : wsStepList) {
                            if (batchWorkDetailList.stream().noneMatch(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(wsStep.getStep().getId()) && batchWorkDetail.getFinish() == Constants.INT_ONE)) {
                                throw new ResponseException("error.notExistTodoStep", "工单待做工序为【" + wsStep.getStep().getName() + ",当前工位不可生产该工序");
                            }
                        }
                    }
                    throw new ResponseException("error.notExistTodoStep", "当前工位无工序可生产");
                } else {
                    if (CollectionUtils.isEmpty(batchWorkDetailList)) {
                        throw new ResponseException("error.notExistTodoStep", "工单待做工序为【" + lists.get(Constants.INT_ZERO).get(Constants.INT_ZERO).getStep().getName()
                                + ",当前选择工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                    }
                    for (List<WsStep> wsStepList : lists) {
                        for (WsStep wsStep : wsStepList) {
                            if (batchWorkDetailList.stream().noneMatch(batchWorkDetail -> batchWorkDetail.getStep().getId().equals(wsStep.getStep().getId()) && batchWorkDetail.getFinish() == Constants.INT_ONE)) {
                                throw new ResponseException("error.notExistTodoStep", "工单待做工序为【" + wsStep.getStep().getName()
                                        + ",当前工位不可生产该工序。工位" + messgesWorkCell.getName() + "【" + messgesWorkCell.getCode() + "】可以生产该工单");
                            }
                        }
                    }
                }
            }
        }
        //验证工序快照的请求模式是否为容器或者SN，若是则返回提示信息
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.CONTAINER_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.containerRequestModeError", "请扫描容器进行请求待做工序");
        }
        if (nextTodoWsStep.getRequestMode() == ConstantsEnum.SN_REQUEST_MODE.getCategoryName()) {
            throw new ResponseException("error.snRequestModeError", "请扫描单支SN进行请求待做工序");
        }
        //初始化返回Rworker待做工序
        RworkerBatchToDoStepGetDTO rworkerBatchToDoStepGetDTO = new RworkerBatchToDoStepGetDTO(nextTodoWsStep);
        rworkerBatchToDoStepGetDTO.setWsStepInfoList(wsSteps.stream().map(WsStepGetInfo::new).collect(Collectors.toList()));
        return rworkerBatchToDoStepGetDTO;
    }

    private WsStep findNextTodoWsStep
            (List<WsStep> wsStepList, List<Step> stepList, List<BatchWorkDetail> batchWorkDetailList) {
        //查找工艺快照中的第一个且工位匹配的工序
        List<WsStep> wsStepCollect = wsStepList.stream().filter(wsStep -> !ValidateUtils.isValid(wsStep.getPreStepId())).toList();
        if (ValidateUtils.isValid(stepList)) {
            wsStepCollect = wsStepList.stream().filter(wsStep -> stepList.stream().anyMatch(step -> step.getId().equals(wsStep.getStep().getId()))).toList();
        }
        //若不存在批量详情且工位能匹配第一个工序则待做工序为快照的第一个工序
        if (!ValidateUtils.isValid(batchWorkDetailList) && ValidateUtils.isValid(wsStepCollect)) {
            return wsStepCollect.get(Constants.INT_ZERO);
        }
        if (!ValidateUtils.isValid(batchWorkDetailList) && !ValidateUtils.isValid(wsStepCollect)) {
            return null;
        }
        List<List<WsStep>> lists = commonService.dealWsStep(wsStepList);
        //按照工序对工单详情进行分组
        Map<Long, List<BatchWorkDetail>> batchWorkDetailGroup = batchWorkDetailList.stream().collect(Collectors.groupingBy(batchWorkDetail -> batchWorkDetail.getStep().getId()));
        for (List<WsStep> wsSteps : lists) {
            List<WsStep> mayNextToDoWsSteps = wsSteps;
            if (ValidateUtils.isValid(stepList)) {
                mayNextToDoWsSteps = wsSteps.stream().filter(wsStep -> stepList.stream().anyMatch(step -> step.getId().equals(wsStep.getStep().getId()))).toList();
            }
            if (!ValidateUtils.isValid(mayNextToDoWsSteps)) {
                continue;
            }
            for (WsStep wsStep : mayNextToDoWsSteps) {
                List<BatchWorkDetail> batchWorkDetails = batchWorkDetailGroup.get(wsStep.getStep().getId());
                BatchWorkDetail batchWorkDetail = ValidateUtils.isValid(batchWorkDetails) ? batchWorkDetails.get(Constants.INT_ZERO) : null;
                // 若工序已完成则继续判断下一个待做工序
                if (null != batchWorkDetail && batchWorkDetail.getFinish() == ConstantsEnum.FINISH_STATUS.getCategoryName()) {
                    continue;
                }
                // 第一个工序未完成则返回该待做工序
                if (StringUtils.isBlank(wsStep.getPreStepId()) && (null == batchWorkDetail || batchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName())) {
                    return wsStep;
                }
                // 前置工序未完成则继续判断下一个匹配到待做工序
                if (Arrays.stream(wsStep.getPreStepId().split(Constants.STR_COMMA))
                        .anyMatch(preStepId -> batchWorkDetailGroup.get(Long.parseLong(preStepId)) == null ||
                                batchWorkDetailGroup.get(Long.parseLong(preStepId)).stream().anyMatch(currBatchWorkDetail -> currBatchWorkDetail.getFinish() == ConstantsEnum.UNFINISHED_STATUS.getCategoryName()))) {
                    continue;
                }
                // 若前置工序都已完成将当前未完成的待做工序返回
                return wsStep;
            }
        }
        return null;
    }
}