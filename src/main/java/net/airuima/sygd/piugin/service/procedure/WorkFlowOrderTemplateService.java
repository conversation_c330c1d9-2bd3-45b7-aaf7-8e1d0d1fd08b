package net.airuima.sygd.piugin.service.procedure;

import com.google.common.collect.Lists;
import net.airuima.constant.Constants;
import net.airuima.domain.base.pedigree.Pedigree;
import net.airuima.domain.base.pedigree.PedigreeConfig;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.process.WorkFlowStep;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.dto.bom.MaterialDTO;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.repository.base.pedigree.PedigreeConfigRepository;
import net.airuima.repository.base.process.StepRepository;
import net.airuima.repository.base.process.WorkFlowStepRepository;
import net.airuima.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.service.base.process.WorkFlowStepService;
import net.airuima.sygd.piugin.enums.WorkFlowOrderTemplateEnum;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.OrderTemplateDataDTO;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 模板Service
 *
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFlowOrderTemplateService {

    private Logger log = LoggerFactory.getLogger(WorkFlowOrderTemplateService.class);

    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    @Autowired
    private WorkFlowStepRepository workFlowStepRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private PedigreeConfigRepository pedigreeConfigRepository;

    @Autowired
    private WorkFlowStepService workFlowStepService;

    /**
     * 填充模板数据
     *
     * @param subWorkSheet 子工单
     * @param staffDTO     制单人
     * @param book         excel工作簿
     * @param stepIdList   工序id集合
     */
    public void fillTemplateData(SubWorkSheet subWorkSheet, StaffDTO staffDTO, Workbook book, List<Long> stepIdList) {
        //解析excel模板变量数据
        OrderTemplateDataDTO templateDataDTO = getTemplateData(subWorkSheet, staffDTO, book, stepIdList);
        // 获取表格所有的sheet
        for (int i = 0; i < book.getNumberOfSheets(); i++) {
            Sheet sheet = book.getSheetAt(i);
            fillSheet(templateDataDTO, sheet, i);
        }
    }


    /**
     * 填充sheet
     *
     * @param templateDataDTO 模板数据
     * @param sheet           表格
     * @param sheetIndex      sheet索引
     * @return Integer 最大行号
     */
    private Integer fillSheet(OrderTemplateDataDTO templateDataDTO, Sheet sheet, Integer sheetIndex) {
        Map<String, List<String>> commonDataMap = templateDataDTO.getCommonDataMap();
        List<Map<String, Object>> rowDataList = templateDataDTO.getRowDataList();
        // 记录最大的行号
        AtomicReference<Integer> maxRowNumber = new AtomicReference<>(0);
        // 填充表头通用的数据
        if (!CollectionUtils.isEmpty(commonDataMap)) {
            commonDataMap.forEach((key, value) -> {
                List<Integer> positionList = new ArrayList<>();
                // 获取关键字的位置
                getStringPosition(key, sheet, positionList);
                //获取行号
                if (!CollectionUtils.isEmpty(positionList)) {
                    int rowIndex = positionList.get(0);
                    //获取列号
                    int columnIndex = positionList.get(1);
                    //使用数据填充关键字
                    fillStringWithData(value, sheet, rowIndex, columnIndex);
                    //记录最大的行号
                    maxRowNumber.set(Math.max(maxRowNumber.get(), rowIndex));
                }
            });
        }
        if (!CollectionUtils.isEmpty(rowDataList)) {
            for (int i = 0; i < rowDataList.size(); i++) {
                Map<String, Object> rowData = rowDataList.get(i);
                for (Map.Entry<String, Object> entry : rowData.entrySet()) {
                    String templateKey = entry.getKey();
                    List<Object> dataList = (List<Object>) entry.getValue();
                    List<Integer> positionList = new ArrayList<>();
                    // 获取关键字的位置
                    getStringPosition(templateKey, sheet, positionList);
                    //校验位置数据
                    if (CollectionUtils.isEmpty(positionList) || positionList.size() != Constants.INT_TWO) {
                        continue;
                    }
                    List<String> dataStringList = new ArrayList<>();
                    //处理没有数据的模板变量
                    if (!CollectionUtils.isEmpty(dataList)) {
                        dataStringList = dataList.stream().filter(Objects::nonNull).map(Object::toString).toList();
                    }
                    //获取行号
                    int rowIndex = positionList.get(0);
                    //获取列号
                    int columnIndex = positionList.get(1);
                    //使用数据填充关键字
                    fillStringWithData(dataStringList, sheet, rowIndex, columnIndex);
                }
            }
        }
        // 清理没有数据的模板变量
        clearTemplateString(sheet);
        return maxRowNumber.get();
    }

    /**
     * 使用数据填充关键字
     *
     * @param dataList    数据集合
     * @param sheet       表格
     * @param rowIndex    行号
     * @param columnIndex 列号
     */
    private void fillStringWithData(List<String> dataList, Sheet sheet, int rowIndex, int columnIndex) {
        if (CollectionUtils.isEmpty(dataList)) {
            dataList = Lists.newArrayList("");
        }
        // 模板数据下标
        int currentDataIndex = 0;
        for (int i = rowIndex; i < (rowIndex + dataList.size()); i++) {
            //获取行
            Row row = sheet.getRow(i);
            if (Objects.isNull(row)) {
                row = sheet.createRow(i);
            }
            for (int j = 0; j <= columnIndex; j++) {
                //获取列
                Cell cell = row.getCell(j, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                if (j == columnIndex) {
                    //填充数据
                    cell.setCellValue(dataList.get(currentDataIndex));
                    //自适应宽度
                    sheet.autoSizeColumn(columnIndex);
                    currentDataIndex = currentDataIndex + 1;
                }
            }
        }
    }

    /**
     * 获取关键字的位置
     *
     * @param templateString 关键字
     * @param sheet          表格
     * @param positionList   位置集合
     */
    private void getStringPosition(String templateString, Sheet sheet, List<Integer> positionList) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell.getCellType().equals(CellType.STRING)) {
                    if (cell.getRichStringCellValue().getString().trim().equals(templateString)) {
                        positionList.add(row.getRowNum());
                        positionList.add(cell.getColumnIndex());
                    }
                }
            }
        }
    }

    /**
     * 清理没有数据的模板变量
     *
     * @param sheet 表格
     */
    public void clearTemplateString(Sheet sheet) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell.getCellType().equals(CellType.STRING)) {
                    if (cell.getRichStringCellValue().getString().trim().startsWith("${")) {
                        //填充数据
                        cell.setCellValue("");
                    }
                }
            }
        }
    }

    /**
     * 获取模板变量填充数据
     *
     * @param book excel 工作簿
     * @return net.airuima.dto.report.TemplateDataDTO  模板变量填充数据
     */
    public OrderTemplateDataDTO getTemplateData(SubWorkSheet subWorkSheet, StaffDTO staffDTO, Workbook book, List<Long> stepIdList) {
        OrderTemplateDataDTO templateDataDTO = new OrderTemplateDataDTO();
        //获取所有的模板变量
        List<String> templateStringList = WorkFlowOrderTemplateEnum.getTemplateStringMap().keySet().stream().toList();
        //获取在excel中定义的模板变量
        List<String> currentTemplateStringList = templateStringList.stream().filter(s -> existInExcel(s, book)).toList();
        Map<String, List<String>> commonDataMap = new HashMap<>();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        Pedigree pedigree = Optional.ofNullable(workSheet).map(WorkSheet::getPedigree).orElse(null);
        // 填充工单
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.SUB_WORKSHEET.getTemplateString())) {
            commonDataMap.put(WorkFlowOrderTemplateEnum.SUB_WORKSHEET.getTemplateString(), Lists.newArrayList(Optional.of(subWorkSheet).map(SubWorkSheet::getSerialNumber).orElse("")));
        }
        //填充产品谱系
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.PEDIGREE_CODE.getTemplateString())) {
            commonDataMap.put(WorkFlowOrderTemplateEnum.PEDIGREE_CODE.getTemplateString(), Lists.newArrayList(Optional.of(Optional.ofNullable(pedigree).map(Pedigree::getCode).orElse("")).orElse("")));
        }
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.PEDIGREE_NAME.getTemplateString())) {
            commonDataMap.put(WorkFlowOrderTemplateEnum.PEDIGREE_NAME.getTemplateString(), Lists.newArrayList(Optional.ofNullable(pedigree).map(Pedigree::getName).orElse("")));
        }
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.PEDIGREE_SPECIFICATION.getTemplateString())) {
            commonDataMap.put(WorkFlowOrderTemplateEnum.PEDIGREE_SPECIFICATION.getTemplateString(), Lists.newArrayList(Optional.ofNullable(pedigree).map(Pedigree::getSpecification).orElse("")));
        }
        // 填充制单人
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.MAKER.getTemplateString())) {
            commonDataMap.put(WorkFlowOrderTemplateEnum.MAKER.getTemplateString(), Lists.newArrayList(Optional.ofNullable(staffDTO).map(StaffDTO::getName).orElse("")));
        }
        // 填充工单创建时间
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.WORKSHEET_CREATE_TIME.getTemplateString())) {
            // 时间格式化 yyyy-MM-dd HH:mm:ss
            String createTime = Optional.ofNullable(workSheet).map(WorkSheet::getCreatedDate).map(time -> DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.systemDefault()).format(time)).orElse("");
            commonDataMap.put(WorkFlowOrderTemplateEnum.WORKSHEET_CREATE_TIME.getTemplateString(), Lists.newArrayList(createTime));
        }
        // 填充子工单数量
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.SUB_WORKSHEET_NUMBER.getTemplateString())) {
            Integer number = Optional.of(subWorkSheet).map(SubWorkSheet::getNumber).orElse(Constants.INT_ZERO);
            commonDataMap.put(WorkFlowOrderTemplateEnum.SUB_WORKSHEET_NUMBER.getTemplateString(), Lists.newArrayList(String.valueOf(number)));
        }
        // 填充分单数量
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.PEDIGREE_SPLIT_NUMBER.getTemplateString())) {
            PedigreeConfig pedigreeConfig = pedigreeConfigRepository.findByPedigreeIdAndDeleted(pedigree.getId(), Constants.LONG_ZERO).orElse(null);
            Integer splitNumber = Optional.ofNullable(pedigreeConfig).map(PedigreeConfig::getSplitNumber).orElse(Constants.INT_ZERO);
            commonDataMap.put(WorkFlowOrderTemplateEnum.PEDIGREE_SPLIT_NUMBER.getTemplateString(), Lists.newArrayList(String.valueOf(splitNumber)));
        }
        // 获取工单的领料单
        List<WsCheckMaterialDetail> wsCheckMaterialDetailList = wsCheckMaterialDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        List<Map<String, Object>> rowDataList = new ArrayList<>();
        //领料单产品名称
        List<String> wsMaterialNameList = new ArrayList<>();
        //领料单产品编码
        List<String> wsMaterialCodeList = new ArrayList<>();
        //领料单产品批次号
        List<String> wsMaterialBatchList = new ArrayList<>();
        // 领料单产品型号
        List<String> wsMaterialSpecificationList = new ArrayList<>();
        for (WsCheckMaterialDetail wsCheckMaterialDetail : wsCheckMaterialDetailList) {
            wsMaterialNameList.add(Optional.ofNullable(wsCheckMaterialDetail).map(WsCheckMaterialDetail::getMaterialDto).map(MaterialDTO::getName).orElse(""));
            wsMaterialCodeList.add(Optional.ofNullable(wsCheckMaterialDetail).map(WsCheckMaterialDetail::getMaterialDto).map(MaterialDTO::getCode).orElse(""));
            wsMaterialBatchList.add(Optional.ofNullable(wsCheckMaterialDetail).map(WsCheckMaterialDetail::getBatch).orElse(""));
            wsMaterialSpecificationList.add(Optional.ofNullable(wsCheckMaterialDetail).map(WsCheckMaterialDetail::getMaterialDto).map(MaterialDTO::getSpecification).orElse(""));
        }
        Map<String, Object> row = new HashMap<>();
        // 领料单产品名称
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.WS_MATERIAL_NAME.getTemplateString())) {
            row.put(WorkFlowOrderTemplateEnum.WS_MATERIAL_NAME.getTemplateString(), wsMaterialNameList);
            rowDataList.add(row);
        }
        // 领料单产品编码
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.WS_MATERIAL_CODE.getTemplateString())) {
            row.put(WorkFlowOrderTemplateEnum.WS_MATERIAL_CODE.getTemplateString(), wsMaterialCodeList);
            rowDataList.add(row);
        }
        // 领料单产品批次号
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.WS_MATERIAL_BATCH.getTemplateString())) {
            row.put(WorkFlowOrderTemplateEnum.WS_MATERIAL_BATCH.getTemplateString(), wsMaterialBatchList);
            rowDataList.add(row);
        }
        // 领料单产品型号
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.WS_MATERIAL_SPECIFICATION.getTemplateString())) {
            row.put(WorkFlowOrderTemplateEnum.WS_MATERIAL_SPECIFICATION.getTemplateString(), wsMaterialSpecificationList);
            rowDataList.add(row);
        }
        WorkFlow workFlow = workSheet.getWorkFlow();
        List<WorkFlowStep> workFlowSteps = workFlowStepRepository.findByWorkFlowIdAndDeleted(workFlow.getId(), Constants.LONG_ZERO);
        List<WorkFlowStep> workFlowStepList = new ArrayList<>();
        //排序工序
        workFlowStepService.findTreeWorkFlowStep(workFlowSteps, null, workFlowStepList);
        List<String> stepNameList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(workFlowStepList)) {
            for (WorkFlowStep workFlowStep : workFlowStepList) {
                stepNameList.add(Optional.of(workFlowStep).map(WorkFlowStep::getStep).map(Step::getName).orElse(""));
            }
        }
        // 设置工序
        if (currentTemplateStringList.contains(WorkFlowOrderTemplateEnum.STEP_NAME.getTemplateString())) {
            row.put(WorkFlowOrderTemplateEnum.STEP_NAME.getTemplateString(), stepNameList);
            rowDataList.add(row);
        }
        templateDataDTO.setCommonDataMap(commonDataMap).setRowDataList(rowDataList);
        return templateDataDTO;
    }


    /**
     * 判断模板变量是否存在excel文件中
     *
     * @param templateString 模板变量
     * @param book           excel 工作簿
     * @return boolean true存在 false不存在
     */
    private boolean existInExcel(String templateString, Workbook book) {
        // 遍历所有sheet
        for (int i = 0; i < book.getNumberOfSheets(); i++) {
            Sheet sheet = book.getSheetAt(i);
            // 遍历行与列
            for (Row row : sheet) {
                for (Cell cell : row) {
                    if (cell.getCellType().equals(CellType.STRING)) {
                        //找到模板变量
                        if (cell.getRichStringCellValue().getString().trim().equals(templateString)) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }


}
