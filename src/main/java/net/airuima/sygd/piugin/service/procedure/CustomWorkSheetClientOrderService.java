package net.airuima.sygd.piugin.service.procedure;

import com.google.common.collect.Lists;
import net.airuima.constant.Constants;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.repository.procedure.aps.WorkSheetRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder;
import net.airuima.sygd.piugin.repository.procedure.batch.CustomWorkSheetClientOrderRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.CustomWorkSheetClientRepository;
import net.airuima.sygd.piugin.util.StringArrayUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单订单信息Service
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CustomWorkSheetClientOrderService extends CommonJpaService<CustomWorkSheetClientOrder> {

    @Autowired
    private CustomWorkSheetClientOrderRepository customWorkSheetClientOrderRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;
    @Autowired
    private CustomWorkSheetClientRepository customWorkSheetClientRepository;

    /**
     * 保存客户订单
     *
     * @param requestCustomWorkSheetClientOrder 客户订单信息
     * @return net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient 客户信息
     */
    public CustomWorkSheetClientOrder saveWorkSheetClientOrder(CustomWorkSheetClientOrder requestCustomWorkSheetClientOrder) {
        WorkSheet workSheet = requestCustomWorkSheetClientOrder.getWorkSheet();
        if (Objects.isNull(workSheet) || Objects.isNull(workSheet.getId())) {
            throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
        }
        WorkSheet queryWorkSheet = workSheetRepository.findByIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(queryWorkSheet)) {
            throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
        }
        String clientOrder = requestCustomWorkSheetClientOrder.getClientOrder();
        Long id = requestCustomWorkSheetClientOrder.getId();
        //新增
        if (Objects.isNull(id)) {
            Optional<CustomWorkSheetClientOrder> workSheetClientOrderOptional = customWorkSheetClientOrderRepository.findByWorkSheetIdAndClientCodeAndDeleted(workSheet.getId(), clientOrder, Constants.LONG_ZERO);
            if (workSheetClientOrderOptional.isPresent()) {
                throw new ResponseException("error.ClientExist", "记录已存在");
            }
            //保存客户订单信息
            CustomWorkSheetClientOrder saveCustomWorkSheetClientOrder = new CustomWorkSheetClientOrder();
            BeanUtils.copyProperties(requestCustomWorkSheetClientOrder, saveCustomWorkSheetClientOrder);
            customWorkSheetClientOrderRepository.save(saveCustomWorkSheetClientOrder);
        } else {
            Optional<CustomWorkSheetClientOrder> queryCustomWorkSheetClientOrderOptional = customWorkSheetClientOrderRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
            if (queryCustomWorkSheetClientOrderOptional.isPresent()) {
                CustomWorkSheetClientOrder queryCustomWorkSheetClientOrder = queryCustomWorkSheetClientOrderOptional.get();
                if (!Objects.equals(queryCustomWorkSheetClientOrder.getWorkSheet().getId(), workSheet.getId()) ||
                        !Objects.equals(queryCustomWorkSheetClientOrder.getClientOrder(), requestCustomWorkSheetClientOrder.getClientCode())) {
                    Optional<CustomWorkSheetClientOrder> customWorkSheetClientOrderOptional = customWorkSheetClientOrderRepository.findByWorkSheetIdAndClientCodeAndDeleted(workSheet.getId(), requestCustomWorkSheetClientOrder.getClientCode(), Constants.LONG_ZERO);
                    if (customWorkSheetClientOrderOptional.isPresent() && !Objects.equals(customWorkSheetClientOrderOptional.get().getId(), id)) {
                        throw new ResponseException("error.ClientExist", "记录已存在");
                    }
                }
                customWorkSheetClientOrderRepository.save(queryCustomWorkSheetClientOrder);
            }
        }
        resetWorkSheetOrder(queryWorkSheet, false, null);
        return requestCustomWorkSheetClientOrder;
    }

    /**
     * 删除实体
     *
     * @param ids 主键id
     */
    public void deleteEntity(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        for (Long id : ids) {
            Optional<CustomWorkSheetClientOrder> queryCustomWorkSheetClientOrderOptional = customWorkSheetClientOrderRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
            if (queryCustomWorkSheetClientOrderOptional.isEmpty()) {
                throw new ResponseException("error.ClientOrderNotExist", "记录不存在");
            }
            CustomWorkSheetClientOrder queryCustomWorkSheetClientOrder = queryCustomWorkSheetClientOrderOptional.get();
            //重置工单客户信息
            WorkSheet queryWorkSheet = workSheetRepository.findByIdAndDeleted(queryCustomWorkSheetClientOrder.getWorkSheet().getId(), Constants.LONG_ZERO).orElse(null);
            customWorkSheetClientOrderRepository.logicDelete(id);
            if (Objects.nonNull(queryWorkSheet)) {
                resetWorkSheetOrder(queryWorkSheet, true, queryCustomWorkSheetClientOrder.getClientOrder());
            }
        }

    }


    /**
     * 重置工单工单信息
     *
     * @param workSheet 工单信息
     */
    public void resetWorkSheetOrder(WorkSheet workSheet, Boolean isDelete, String deleteClientOrder) {
        if (Objects.isNull(workSheet)) {
            return;
        }
        List<CustomWorkSheetClientOrder> workSheetClientOrderList = customWorkSheetClientOrderRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(workSheetClientOrderList)) {
            if (isDelete) {
                workSheet.setCustom2(null);
                workSheetRepository.save(workSheet);
                return;
            } else {
                return;
            }
        }
        List<String> requestClientOrderCodeList = new ArrayList<>();
        for (CustomWorkSheetClientOrder workSheetClientOrder : workSheetClientOrderList) {
            if (!ObjectUtils.isEmpty(workSheetClientOrder.getClientCode())) {
                String cleanOrderCode = workSheetClientOrder.getClientOrder().trim().replaceAll("\\s", "");
                if (!StringUtils.isBlank(cleanOrderCode)) {
                    requestClientOrderCodeList.add(cleanOrderCode);
                }

            }
        }
        //Custom1字段
        String custom2 = workSheet.getCustom2();
        if (StringUtils.isNotBlank(custom2)) {
            List<String> dbClientOrderList = StringArrayUtils.stringToArray(custom2.trim());
            List<String> finalList = new ArrayList<>();
            if (!isDelete) {
                // 不是删除操作，需要合并
                if (!CollectionUtils.isEmpty(dbClientOrderList)) {
                    for (String clientCode : dbClientOrderList) {
                        if (!finalList.contains(clientCode) && !ObjectUtils.isEmpty(clientCode)) {
                            finalList.add(clientCode);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(requestClientOrderCodeList)) {
                    for (String requestClientOrderCode : requestClientOrderCodeList) {
                        if (!finalList.contains(requestClientOrderCode) && !ObjectUtils.isEmpty(requestClientOrderCode)) {
                            finalList.add(requestClientOrderCode);
                        }
                    }
                }
            } else {
                if(!CollectionUtils.isEmpty(dbClientOrderList)){
                    for (String orderCode : dbClientOrderList) {
                        if (!finalList.contains(orderCode) &&!ObjectUtils.isEmpty(orderCode)) {
                            finalList.add(orderCode);
                        }
                    }
                }
                // 删除操作，需要删除
                if (!ObjectUtils.isEmpty(deleteClientOrder)) {
                    String removeCode = deleteClientOrder.trim().replaceAll("\\s", "");
                    finalList.remove(removeCode);
                }
            }
            // 订单是custom2字段
            workSheet.setCustom2(StringArrayUtils.arrayToString(finalList));
            workSheetRepository.save(workSheet);
        } else {
            custom2 = StringArrayUtils.arrayToString(requestClientOrderCodeList);
            workSheet.setCustom2(custom2);
            workSheetRepository.save(workSheet);
        }
    }


    /**
     * 根据工单编码查询客户订单信息
     *
     * @param serialNumber 工单编码
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder> 客户订单信息
     */
    @Transactional(readOnly = true)
    public List<CustomWorkSheetClientOrder> findByWorkSheet(String serialNumber) {
        if (StringUtils.isBlank(serialNumber)) {
            return Lists.newArrayList();
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        if (workSheetOptional.isEmpty()) {
            return Lists.newArrayList();
        }
        WorkSheet workSheet = workSheetOptional.get();
        return customWorkSheetClientOrderRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
    }

    /**
     * 批量保存工单订单
     *
     * @param entityList 工单订单信息
     */
    public void bathSaveWorkSheetClientOrder(List<CustomWorkSheetClientOrder> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        for (CustomWorkSheetClientOrder entity : entityList) {
            saveWorkSheetClientOrder(entity);
        }
    }
}

