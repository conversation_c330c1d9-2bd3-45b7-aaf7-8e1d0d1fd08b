package net.airuima.sygd.piugin.service.procedure.batch;

import com.alibaba.fastjson.JSON;
import net.airuima.client.feign.bom.MaterialFeignClient;
import net.airuima.client.feign.organization.StaffFeignClient;
import net.airuima.client.feign.rfms.FacilityFeignClient;
import net.airuima.constant.Constants;
import net.airuima.domain.base.pedigree.Pedigree;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.quality.CheckItem;
import net.airuima.domain.base.quality.UnqualifiedItem;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.base.wearingpart.WearingPart;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.dto.bom.MaterialDTO;
import net.airuima.dto.client.base.BaseClientDTO;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.dto.rfms.FacilityDTO;
import net.airuima.repository.base.pedigree.PedigreeRepository;
import net.airuima.repository.base.process.StepRepository;
import net.airuima.repository.base.process.WorkFlowRepository;
import net.airuima.repository.base.quality.CheckItemRepository;
import net.airuima.repository.base.quality.UnqualifiedItemRepository;
import net.airuima.repository.base.scene.WorkCellRepository;
import net.airuima.repository.base.wearingpart.WearingPartRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.repository.procedure.batch.ContainerRepository;
import net.airuima.service.client.ClientStepService;
import net.airuima.service.procedure.batch.api.IRollbackStepService;
import net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.sygd.piugin.client.feign.ocmes.GlueMaterialBatchAdaptFeignClient;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer;
import net.airuima.sygd.piugin.repository.procedure.batch.ChangeContainerRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerAdaptRepository;
import net.airuima.sygd.piugin.util.BeanUtilsWithListCopy;
import net.airuima.sygd.piugin.util.TimeUtils;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDataDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MesDataRequestDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MesDataResponseDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device.*;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device.ClientSaveStepInfoDTO.*;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseDataUtils;
import net.airuima.util.ValidateUtils;
import net.airuima.web.rest.client.dto.ClientContainerRollBakeDTO;
import net.airuima.web.rest.procedure.batch.dto.RollBackDTO;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ClientStepAdaptService {

    @Autowired
    private IRollbackStepService[] rollbackStepServices;

    @Autowired
    private ChangeContainerRepository changeContainerRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private ContainerAdaptRepository containerAdaptRepository;

    @Autowired
    private ClientStepService clientStepService;

    @Autowired
    private CheckItemRepository checkItemRepository;

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private StepRepository stepRepository;

    @Autowired
    private StaffFeignClient staffFeignClient;

    @Autowired
    private WorkCellRepository workCellRepository;
    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private ContainerRepository containerRepository;

    @Autowired
    private MaterialFeignClient materialFeignClient;


    @Autowired
    private FacilityFeignClient facilityFeignClient;

    @Autowired
    private WearingPartRepository wearingPartRepository;

    @Autowired
    private PedigreeRepository pedigreeRepository;

    @Autowired
    private GlueMaterialBatchAdaptFeignClient glueMaterialBatchAdaptFeignClient;

    @Autowired
    private UnqualifiedItemRepository unqualifiedItemRepository;

    private static final Logger log = LoggerFactory.getLogger(ClientStepAdaptService.class);


    /**
     * Rworker调用容器回退
     *
     * @param rollBackDto 容器回退信息
     * @return ClientContainerRollBakeDTO
     * <AUTHOR>
     * @date 2022/11/28
     */
    public ClientContainerRollBakeDTO rollBakeContainerDetail(RollBackDTO rollBackDto) {
        // 回退容器转换
        ContainerDetail containerDetail = containerDetailRepository.findByIdAndDeleted(rollBackDto.getContainerDetailId(), Constants.LONG_ZERO);
        if (containerDetail != null) {
            Container container = containerDetail.getContainer();
            WorkCell workCell = containerDetail.getWorkCell();
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
            Step step = batchWorkDetail.getStep();
            //查找容器转换记录
            ChangeContainer changeContainer = changeContainerRepository.findBySubWorkSheetIdAndWorkCellIdAndContainerIdAndStepIdAndDeleted(subWorkSheet.getId(), workCell.getId(), container.getId(), step.getId(), Constants.LONG_ZERO);
            if (changeContainer != null) {
                Container originContainer = changeContainer.getOriginContainer();
                // 更新原容器当前数量 custom2为容器当前数量
                originContainer.setCustom2(changeContainer.getCustom1());
                containerAdaptRepository.save(originContainer);
                container.setCustom2(changeContainer.getCustom2());
                containerAdaptRepository.save(container);
                changeContainerRepository.logicDelete(changeContainer);
            }
        }
        //容器回退
        ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].deleteContainerDetailById(rollBackDto);
        if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
            return new ClientContainerRollBakeDTO(Constants.KO, containerDetailReplaceDto.getMessage());
        }
        if (Constants.OK.equals(containerDetailReplaceDto.getStatus()) && ValidateUtils.isValid(containerDetailReplaceDto.getContainerDetailList())) {
            List<ClientContainerRollBakeDTO.ClientContainerDetailInfo> clientContainerDetailInfos = containerDetailReplaceDto.getContainerDetailList().stream().map(ClientContainerRollBakeDTO.ClientContainerDetailInfo::new)
                    .collect(Collectors.toList());
            return new ClientContainerRollBakeDTO(Constants.KO, "原容器已被占用需替换容器", rollBackDto.getContainerDetailId(), clientContainerDetailInfos);
        }
        return new ClientContainerRollBakeDTO(Constants.OK, "容器回退成功");
    }

    /**
     * 获取工序信息
     *
     * @param customStepInfo 请求传过来的工序信息
     * @return net.airuima.dto.client.ClientGetStepInfoDTO 工序信息
     */
    public ClientGetStepInfoDTO getStepInfo(ClientGetStepInfoDTO.StepInfo customStepInfo) {
        log.info("获取工序信息请求参数:{}", JSON.toJSONString(customStepInfo));
        String subWorkSheetCode = customStepInfo.getSubWorkSheetCode();
        String workCellCode = customStepInfo.getWorkCellCode();
        String staffCode = customStepInfo.getStaffCode();
        List<String> requestContainerCodes = customStepInfo.getRequestContainerCodes();
        String bindContainerCode = customStepInfo.getBindContainerCode();
        List<Long> requestContainerIds = customStepInfo.getRequestContainerIds();
        String workCellIp = customStepInfo.getWorkCellIp();
        net.airuima.dto.client.ClientGetStepInfoDTO.StepInfo stepInfo = new net.airuima.dto.client.ClientGetStepInfoDTO.StepInfo();
        stepInfo.setWorkCellIp(workCellIp);
        // 通过对应的编码获取对应的id
        if (!ObjectUtils.isEmpty(workCellCode)) {
            Optional<WorkCell> workCellOptional = workCellRepository.findByCodeAndDeleted(workCellCode, Constants.LONG_ZERO);
            if (workCellOptional.isPresent()) {
                stepInfo.setWorkCellId(workCellOptional.get().getId());
            }
        }
        if (!ObjectUtils.isEmpty(subWorkSheetCode)) {
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetCode, Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()) {
                stepInfo.setSubWorkSheetId(subWorkSheetOptional.get().getId());
            }
        }
        if (!ObjectUtils.isEmpty(staffCode)) {
            StaffDTO staffDTO = ResponseDataUtils.get(staffFeignClient.findByCode(staffCode));
            if (staffDTO != null) {
                stepInfo.setStaffId(staffDTO.getId());
            }
        }
        if (!CollectionUtils.isEmpty(requestContainerCodes)) {
            if (requestContainerIds == null) {
                requestContainerIds = Lists.newArrayList();
            }
            for (String requestContainerCode : requestContainerCodes) {
                Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(requestContainerCode, Constants.LONG_ZERO);
                if (containerOptional.isPresent()) {
                    requestContainerIds.add(containerOptional.get().getId());
                }
            }
            stepInfo.setRequestContainerIds(requestContainerIds);
        }
        if (!ObjectUtils.isEmpty(bindContainerCode)) {
            Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(bindContainerCode, Constants.LONG_ZERO);
            if (containerOptional.isPresent()) {
                stepInfo.setBindContainerId(containerOptional.get().getId());
            }
        }
        log.info("获取工序信息请求参数解析后:{}", JSON.toJSONString(stepInfo));
        net.airuima.dto.client.ClientGetStepInfoDTO stepInfoResp = clientStepService.getStepInfo(stepInfo);
        ClientGetStepInfoDTO clientGetStepInfoDTO = new ClientGetStepInfoDTO();
        BeanUtils.copyProperties(stepInfoResp, clientGetStepInfoDTO);
        log.info("获取工序信息响应参数:{}", JSON.toJSONString(clientGetStepInfoDTO));
        return clientGetStepInfoDTO;
    }

    /**
     * 保存工序信息
     *
     * @param customClientSaveStepInfoDto 保存工序信息
     * @return net.airuima.dto.client.base.BaseClientDTO 保存结果
     */
    public BaseClientDTO saveStepInfo(ClientSaveStepInfoDTO customClientSaveStepInfoDto) {
        log.info("保存工序信息请求参数:{}", JSON.toJSONString(customClientSaveStepInfoDto));
        transferCodeToId(customClientSaveStepInfoDto);
        String startTimeText = customClientSaveStepInfoDto.getStartTimeText();
        customClientSaveStepInfoDto.setStartTime(TimeUtils.formatText(startTimeText));
        String endTimeText = customClientSaveStepInfoDto.getEndTimeText();
        customClientSaveStepInfoDto.setEndTime(TimeUtils.formatText(endTimeText));
        List<BakeHistoryInfoDTO> bakeHistoryInfoList = customClientSaveStepInfoDto.getBakeHistoryInfoList();
        List<CycleBakeHistoryInfoDTO> cycleBakeHistoryInfoList = customClientSaveStepInfoDto.getCycleBakeHistoryInfoList();
        List<AgeingHistoryInfoDTO> ageingHistoryInfoList = customClientSaveStepInfoDto.getAgeingHistoryInfoList();
        List<UnqualifiedItemInfo> unqualifiedItemInfoList = customClientSaveStepInfoDto.getUnqualifiedItemInfoList();
        handleUnqualified(unqualifiedItemInfoList);
        List<SnInfo> snInfoList = customClientSaveStepInfoDto.getSnInfoList();
        handleSnInfo(snInfoList);
        List<MaterialBatchInfo> materialBatchInfoList = customClientSaveStepInfoDto.getMaterialBatchInfoList();
        handleMaterialBatch(materialBatchInfoList);
        List<EquipmentInfo> equipmentInfoList = customClientSaveStepInfoDto.getEquipmentInfoList();
        handleEquipment(equipmentInfoList);
        List<RequestContainerInfo> requestContainerInfos = customClientSaveStepInfoDto.getRequestContainerInfos();
        handleRequestContainer(requestContainerInfos);
        List<WearingPartInfo> wearingPartInfoList = customClientSaveStepInfoDto.getWearingPartInfoList();
        handleWearingPart(wearingPartInfoList);
        RandomInspectSaveInfo randomInspectSaveInfo = customClientSaveStepInfoDto.getRandomInspectSaveInfo();
        handleInspectSaveInfo(randomInspectSaveInfo);
        String requestDto = JSON.toJSONString(customClientSaveStepInfoDto);
        net.airuima.dto.client.ClientSaveStepInfoDTO clientSaveStepInfoDto =JSON.parseObject(requestDto, net.airuima.dto.client.ClientSaveStepInfoDTO.class);
        log.info("保存工序信息请求参数解析后:{}", JSON.toJSONString(clientSaveStepInfoDto));
        BaseClientDTO baseClientDTO = clientStepService.saveStepInfo(clientSaveStepInfoDto);
        log.info("保存工序信息响应参数:{}", JSON.toJSONString(baseClientDTO));
        return baseClientDTO;
    }

    /**
     * 转换编码为id
     * @param customClientSaveStepInfoDto 保存工序信息
     */
    private void transferCodeToId(ClientSaveStepInfoDTO customClientSaveStepInfoDto) {
        String subWsCode = customClientSaveStepInfoDto.getSubWsCode();
        if (!ObjectUtils.isEmpty(subWsCode)) {
            Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWsCode, Constants.LONG_ZERO);
            if (subWorkSheetOptional.isPresent()) {
                customClientSaveStepInfoDto.setSubWsId(subWorkSheetOptional.get().getId());
            }
        }
        String stepCode = customClientSaveStepInfoDto.getStepCode();
        if (!ObjectUtils.isEmpty(stepCode)) {
            Optional<Step> stepOptional = stepRepository.findByCodeAndDeleted(stepCode, Constants.LONG_ZERO);
            if (stepOptional.isPresent()) {
                customClientSaveStepInfoDto.setStepId(stepOptional.get().getId());
            }
        }
        String staffCode = customClientSaveStepInfoDto.getStaffCode();
        if (!ObjectUtils.isEmpty(staffCode)) {
            StaffDTO staffDTO = ResponseDataUtils.get(staffFeignClient.findByCode(staffCode));
            if (staffDTO != null) {
                customClientSaveStepInfoDto.setStaffId(staffDTO.getId());
            }
        }
        String workCellCode = customClientSaveStepInfoDto.getWorkCellCode();
        if (!ObjectUtils.isEmpty(workCellCode)) {
            Optional<WorkCell> workCellOptional = workCellRepository.findByCodeAndDeleted(workCellCode, Constants.LONG_ZERO);
            if (workCellOptional.isPresent()) {
                customClientSaveStepInfoDto.setWorkCellId(workCellOptional.get().getId());
            }
        }
        String bindContainerCode = customClientSaveStepInfoDto.getBindContainerCode();
        if (!ObjectUtils.isEmpty(bindContainerCode)) {
            Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(bindContainerCode, Constants.LONG_ZERO);
            if (containerOptional.isPresent()) {
                customClientSaveStepInfoDto.setBindContainerId(containerOptional.get().getId());
            }
        }
        String fqcWorkFlowCode = customClientSaveStepInfoDto.getFqcWorkFlowCode();
        if (!ObjectUtils.isEmpty(fqcWorkFlowCode)) {
            Optional<WorkFlow> workFlowOptional = workFlowRepository.findByCodeAndDeleted(fqcWorkFlowCode, Constants.LONG_ZERO);
            if (workFlowOptional.isPresent()) {
                customClientSaveStepInfoDto.setFqcWorkFlowId(workFlowOptional.get().getId());
            }
        }
    }

    /**
     * 处理不合格项
     * @param unqualifiedItemInfoList 不合格项信息
     */
    private void handleUnqualified(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
        try {
            if (!CollectionUtils.isEmpty(unqualifiedItemInfoList)) {
                for (int i = 0; i < unqualifiedItemInfoList.size(); i++) {
                    UnqualifiedItemInfo unqualifiedItemInfo = unqualifiedItemInfoList.get(i);
                    if (!ObjectUtils.isEmpty(unqualifiedItemInfo.getUnqualifiedItemCode())) {
                        Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository.findByCodeAndDeleted(unqualifiedItemInfo.getUnqualifiedItemCode(), Constants.LONG_ZERO);
                        if (unqualifiedItemOptional.isPresent()) {
                            unqualifiedItemInfo.setUnqualifiedItemId(unqualifiedItemOptional.get().getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理SN信息
     * @param snInfoList SN信息
     */
    private void handleSnInfo(List<SnInfo> snInfoList) {
        try {
            if (!CollectionUtils.isEmpty(snInfoList)) {
                for (int i = 0; i < snInfoList.size(); i++) {
                    SnInfo snInfo = snInfoList.get(i);
                    snInfo.setStartTime(TimeUtils.formatText(snInfo.getStartTimeText()));
                    snInfo.setEndTime(TimeUtils.formatText(snInfo.getEndTimeText()));
                    String unqualifiedItemCode = snInfo.getUnqualifiedItemCode();
                    if (!ObjectUtils.isEmpty(unqualifiedItemCode)) {
                        Optional<UnqualifiedItem> unqualifiedItemOptional = unqualifiedItemRepository.findByCodeAndDeleted(unqualifiedItemCode, Constants.LONG_ZERO);
                        if (unqualifiedItemOptional.isPresent()) {
                            snInfo.setUnqualifiedItemId(unqualifiedItemOptional.get().getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理物料批次信息
     * @param materialBatchInfoList 物料批次信息
     */
    private void handleMaterialBatch(List<MaterialBatchInfo> materialBatchInfoList) {
        try {
            // 转换物料id为物料编码
            if (!CollectionUtils.isEmpty(materialBatchInfoList)) {
                for (int i = 0; i < materialBatchInfoList.size(); i++) {
                    MaterialBatchInfo materialBatchInfo = materialBatchInfoList.get(i);
                    if (ObjectUtils.isEmpty(materialBatchInfo.getMaterialCode())) {
                        MaterialDTO materialDTO = ResponseDataUtils.get(materialFeignClient.findByCode(materialBatchInfo.getMaterialCode()));
                        if (materialDTO != null) {
                            materialBatchInfo.setMaterialId(materialDTO.getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理设备信息
     * @param equipmentInfoList 设备信息
     */
    private void handleEquipment(List<EquipmentInfo> equipmentInfoList) {
        try {
            if (!CollectionUtils.isEmpty(equipmentInfoList)) {
                for (int i = 0; i < equipmentInfoList.size(); i++) {
                    EquipmentInfo equipmentInfo = equipmentInfoList.get(i);
                    if (!ObjectUtils.isEmpty(equipmentInfo.getEquipmentCode())) {
                        FacilityDTO facilityDTO = ResponseDataUtils.get(facilityFeignClient.byCode(equipmentInfo.getEquipmentCode()));
                        if (facilityDTO != null) {
                            equipmentInfo.setEquipmentId(facilityDTO.getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * c处理请求容器
     * @param requestContainerInfos 请求容器信息
     */
    private void handleRequestContainer(List<RequestContainerInfo> requestContainerInfos) {
        try {
            if (!CollectionUtils.isEmpty(requestContainerInfos)) {
                for (int i = 0; i < requestContainerInfos.size(); i++) {
                    RequestContainerInfo requestContainerInfo = requestContainerInfos.get(i);
                    if (!ObjectUtils.isEmpty(requestContainerInfo.getContainerCode())) {
                        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(requestContainerInfo.getContainerCode(), Constants.LONG_ZERO);
                        if (containerOptional.isPresent()) {
                            requestContainerInfo.setContainerId(containerOptional.get().getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理易损件
     * @param wearingPartInfoList 易损件信息
     */
    private void handleWearingPart(List<WearingPartInfo> wearingPartInfoList) {
        try {
            if (!CollectionUtils.isEmpty(wearingPartInfoList)) {
                for (int i = 0; i < wearingPartInfoList.size(); i++) {
                    WearingPartInfo wearingPartInfo = wearingPartInfoList.get(i);
                    if (!ObjectUtils.isEmpty(wearingPartInfo.getWearingPartCode())) {
                        Optional<WearingPart> wearingPartOptional = wearingPartRepository.findByCodeAndDeleted(wearingPartInfo.getWearingPartCode(), Constants.LONG_ZERO);
                        if (wearingPartOptional.isPresent()) {
                            wearingPartInfo.setWearingPartId(wearingPartOptional.get().getId());
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理抽检信息
     * @param randomInspectSaveInfo 抽检信息
     */
    private void handleInspectSaveInfo(RandomInspectSaveInfo randomInspectSaveInfo) {
        try {
            if (!ObjectUtils.isEmpty(randomInspectSaveInfo)) {
                List<RandomInspectSaveInfo.SnCheckItemInfo> snCheckItemInfoList = randomInspectSaveInfo.getSnCheckItemInfoList();
                if (!CollectionUtils.isEmpty(snCheckItemInfoList)) {
                    for (int i = 0; i < snCheckItemInfoList.size(); i++) {
                        RandomInspectSaveInfo.SnCheckItemInfo snCheckItemInfo = snCheckItemInfoList.get(i);
                        if (!ObjectUtils.isEmpty(snCheckItemInfo)) {
                            List<RandomInspectSaveInfo.SnCheckItemInfo.CheckItemInfo> checkItemInfoList = snCheckItemInfo.getCheckItemInfoList();
                            if (!CollectionUtils.isEmpty(checkItemInfoList)) {
                                for (int j = 0; j < checkItemInfoList.size(); j++) {
                                    RandomInspectSaveInfo.SnCheckItemInfo.CheckItemInfo checkItemInfo = checkItemInfoList.get(i);
                                    if (!ObjectUtils.isEmpty(checkItemInfo.getCheckItemCode())) {
                                        CheckItem checkItem = checkItemRepository.findByCodeAndDeleted(checkItemInfo.getCheckItemCode(), Constants.LONG_ZERO);
                                        if (checkItem != null) {
                                            checkItemInfo.setCheckItemId(checkItem.getId());
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询MES数据
     *
     * @param mesDataRequestList MES数据请求
     * @return java.util.List<net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MesDataResponseDTO> MES数据响应
     */
    @Transactional(readOnly = true)
    public List<MesDataResponseDTO> queryMesData(List<MesDataRequestDTO> mesDataRequestList) {
        // 记录日志
        log.info("查询MES数据请求参数:{}", JSON.toJSONString(mesDataRequestList));
        if (CollectionUtils.isEmpty(mesDataRequestList)) {
            return Lists.newArrayList();
        }
        List<MesDataResponseDTO> mesDataResponseList = Lists.newArrayList();
        for (MesDataRequestDTO mesDataRequest : mesDataRequestList) {
            String code = mesDataRequest.getCode();
            Integer category = Optional.ofNullable(mesDataRequest.getCategory()).orElse(Constants.INT_ZERO);
            MesDataResponseDTO mesDataResponse = new MesDataResponseDTO();
            //类别 0子工单 1工序 2员工 3工位 4容器 5物料 6产品谱系 7设备 8 易损件
            switch (category) {
                case 0:
                    //子工单
                    Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(code, Constants.LONG_ZERO);
                    if (subWorkSheetOptional.isPresent()) {
                        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
                        mesDataResponse.setCode(subWorkSheet.getSerialNumber());
                        mesDataResponse.setId(subWorkSheet.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 1:
                    //工序
                    Optional<Step> stepOptional = stepRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
                    if (stepOptional.isPresent()) {
                        Step step = stepOptional.get();
                        mesDataResponse.setCode(step.getCode());
                        mesDataResponse.setId(step.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 2:
                    //员工
                    StaffDTO staffDTO = null;
                    try {
                        staffDTO = ResponseDataUtils.get(staffFeignClient.findByCode(code));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (staffDTO != null) {
                        mesDataResponse.setCode(staffDTO.getCode());
                        mesDataResponse.setId(staffDTO.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 3:
                    //工位
                    Optional<WorkCell> workCellOptional = workCellRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
                    if (workCellOptional.isPresent()) {
                        WorkCell workCell = workCellOptional.get();
                        mesDataResponse.setCode(workCell.getCode());
                        mesDataResponse.setId(workCell.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 4:
                    //容器
                    Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
                    if (containerOptional.isPresent()) {
                        Container container = containerOptional.get();
                        mesDataResponse.setCode(container.getCode());
                        mesDataResponse.setId(container.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 5:
                    //物料
                    MaterialDTO materialDTO = null;
                    try {
                        materialDTO = ResponseDataUtils.get(materialFeignClient.findByCode(code));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (materialDTO != null) {
                        mesDataResponse.setCode(materialDTO.getCode());
                        mesDataResponse.setId(materialDTO.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }

                    break;
                case 6:
                    //产品谱系
                    Optional<Pedigree> pedigreeOptional = pedigreeRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
                    if (pedigreeOptional.isPresent()) {
                        Pedigree pedigree = pedigreeOptional.get();
                        mesDataResponse.setCode(pedigree.getCode());
                        mesDataResponse.setId(pedigree.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 7:
                    //设备
                    FacilityDTO facilityDTO = null;
                    try {
                        facilityDTO = ResponseDataUtils.get(facilityFeignClient.byCode(code));
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    if (facilityDTO != null) {
                        mesDataResponse.setCode(facilityDTO.getCode());
                        mesDataResponse.setId(facilityDTO.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                case 8:
                    //易损件
                    Optional<WearingPart> wearingPartOptional = wearingPartRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
                    if (wearingPartOptional.isPresent()) {
                        WearingPart wearingPart = wearingPartOptional.get();
                        mesDataResponse.setCode(wearingPart.getCode());
                        mesDataResponse.setId(wearingPart.getId());
                    } else {
                        mesDataResponse.setCode(code);
                        mesDataResponse.setId(null);
                    }
                    break;
                default:
                    mesDataResponse.setCode(code);
                    mesDataResponse.setId(null);
                    break;
            }
            mesDataResponseList.add(mesDataResponse);

        }
        // 记录返回参数
        return mesDataResponseList;
    }

    /**
     * 验证胶水批次
     *
     * @param materialBatch 胶水批次
     * @return net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDTO 验证结果
     */
    public GlueMaterialBatchVerifyDTO validateGlue(String materialBatch) {
        log.info("验证胶水批次请求参数:{}", JSON.toJSONString(materialBatch));
        GlueMaterialBatchVerifyDTO verifyDTO = new GlueMaterialBatchVerifyDTO();
        if (ObjectUtils.isEmpty(materialBatch)) {
            verifyDTO.setKey("error").setMessage("胶水批次不能为空");
            return verifyDTO;
        }
        ResponseData<GlueMaterialBatchVerifyDataDTO> body = glueMaterialBatchAdaptFeignClient.validateGlue(materialBatch).getBody();
        if (body == null) {
            verifyDTO.setKey("error").setMessage("验证胶水批次失败");
            return verifyDTO;
        }
        if (body.getKey() != null && body.getKey().contains("error")) {
            verifyDTO.setMessage(body.getMessage());
            verifyDTO.setKey("error");
            return verifyDTO;
        }
        GlueMaterialBatchVerifyDataDTO data = body.getData();
        verifyDTO = new GlueMaterialBatchVerifyDTO();
        verifyDTO.setKey("ok").setData(data);
        log.info("验证胶水批次返回参数:{}", JSON.toJSONString(verifyDTO));
        return verifyDTO;
    }
}
