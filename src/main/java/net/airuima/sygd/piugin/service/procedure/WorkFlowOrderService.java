package net.airuima.sygd.piugin.service.procedure;

import com.google.common.io.Files;
import feign.Response;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.client.feign.organization.StaffFeignClient;
import net.airuima.client.feign.rule.SystemCodeFeignClient;
import net.airuima.constant.Constants;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.sygd.piugin.client.feign.document.DocumentFeignClient;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.WorkFlowOrderDTO;
import net.airuima.util.ResponseDataUtils;
import org.apache.commons.io.FileUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程单Service
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class WorkFlowOrderService {

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private StaffFeignClient staffFeignClient;

    @Autowired
    private WorkFlowOrderTemplateService workFlowOrderTemplateService;

    @Autowired
    private SystemCodeFeignClient systemCodeFeignClient;

    @Autowired
    private DocumentFeignClient documentFeignClient;


    /**
     * 获取模板文件
     *
     * @return java.io.File 模板文件
     */
    private File getTemplate() {
        try {
            String templateIdText = ResponseDataUtils.get(systemCodeFeignClient.findByCode("key_work_flow_order_template"));
            if (ObjectUtils.isEmpty(templateIdText)) {
                return null;
            }
            Long documentId = Long.valueOf(templateIdText);
            //下载模板文件
            Response fileResponse = documentFeignClient.download("RMES", Constants.INT_ONE, documentId);
            InputStream inputStream = null;
            // 临时文件名为时间戳
            String templateFileName = String.valueOf(System.currentTimeMillis());
            File templateFile = null;
            if (!Objects.isNull(fileResponse)) {
                inputStream = fileResponse.body().asInputStream();
                //读取header Content-Disposition 提取文件后缀
                Map<String, Collection<String>> headers = fileResponse.headers();
                Collection<String> strings = headers.get("Content-Disposition");
                String fileName = strings.iterator().next();
                String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
                templateFile = File.createTempFile(templateFileName, "." + fileExtension);
                if (templateFile != null) {
                    FileUtils.copyInputStreamToFile(inputStream, templateFile);
                    return templateFile;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * 导出工艺流程单
     *
     * @param workFlowOrderDTO 工艺流程单导出参数
     * @param response         响应
     */
    public void export(WorkFlowOrderDTO workFlowOrderDTO, HttpServletResponse response) throws Exception {
        String subWorkSheetSerialNumber = workFlowOrderDTO.getSubWorkSheetSerialNumber();
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            errorMessage(response, "SubWorkSheetNotFound", "子工单不存在");
            return;
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        StaffDTO staffDTO = null;
        if (Objects.nonNull(workFlowOrderDTO.getOperatorId())) {
            staffDTO = staffFeignClient.findById(workFlowOrderDTO.getOperatorId());
        }
        File templateFile = getTemplate();
        if (templateFile == null) {
            errorMessage(response, "TemplateNotFound", "模板不存在");
            return;
        }
        String fileExtension = Files.getFileExtension(templateFile.getName());
        // 读取模板文件
        Workbook book = null;
        if ("xls".equals(fileExtension)) {
            book = new HSSFWorkbook(FileUtils.openInputStream(templateFile));
        }
        if ("xlsx".equals(fileExtension)) {
            book = new XSSFWorkbook(FileUtils.openInputStream(templateFile));
        }
        String excelName = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + "." + fileExtension;
        workFlowOrderTemplateService.fillTemplateData(subWorkSheet, staffDTO, book, null);
        response.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(excelName, StandardCharsets.UTF_8));
        response.setCharacterEncoding("utf-8");
        book.write(response.getOutputStream());
    }

    /**
     * 错误信息
     *
     * @param response             响应
     * @param errorKey             错误key
     * @param templateErrorMessage 模板错误信息
     */
    private void errorMessage(HttpServletResponse response, String errorKey, String templateErrorMessage) {
        try {
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-error", "error." + errorKey);
            response.setHeader("X-app-params", URLEncoder.encode(templateErrorMessage, "UTF-8"));
            response.setHeader("X-app-error-message", URLEncoder.encode(templateErrorMessage, "UTF-8"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
