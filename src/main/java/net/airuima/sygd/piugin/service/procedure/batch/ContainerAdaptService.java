package net.airuima.sygd.piugin.service.procedure.batch;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.airuima.client.feign.rule.DictionaryFeignClient;
import net.airuima.client.feign.rule.SystemCodeFeignClient;
import net.airuima.constant.Constants;
import net.airuima.domain.base.pedigree.Pedigree;
import net.airuima.domain.base.pedigree.PedigreeConfig;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.dto.base.BaseDTO;
import net.airuima.repository.base.pedigree.PedigreeConfigRepository;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.sygd.piugin.constant.SubWorkSheetExcelConstants;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkSheetContainerRepository;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerCheckDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerCreateDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerResponseDTO;
import net.airuima.util.*;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器插件Service
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ContainerAdaptService extends CommonJpaService<Container> {

    private final ContainerAdaptRepository containerAdaptRepository;

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private WorkSheetContainerRepository workSheetContainerRepository;

    @Autowired
    private DictionaryFeignClient dictionaryFeignClient;

    @Autowired
    private PedigreeConfigRepository pedigreeConfigRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;


    public ContainerAdaptService(ContainerAdaptRepository containerAdaptRepository) {
        this.containerAdaptRepository = containerAdaptRepository;
    }


    /**
     * 通过子工单创建容器
     *
     * @param containerCreateDTO 容器创建DTO
     * @return java.util.List<net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerResponseDTO>容器列表
     */
    public List<ContainerResponseDTO> createContainerBySubWorksheet(ContainerCreateDTO containerCreateDTO) {
        // 子工单编码
        String subWorkSheetSerialNumber = containerCreateDTO.getSubWorkSheetSerialNumber();
        // 转容器前数量
        Integer beforeChangeNumber = containerCreateDTO.getBeforeChangeNumber();
        // 转容器后数量
        Integer afterChangeNumber = containerCreateDTO.getAfterChangeNumber();
        if (beforeChangeNumber != null && afterChangeNumber != null) {
            if (beforeChangeNumber < afterChangeNumber) {
                throw new ResponseException("error.BeforeChangeNumberIsLessThanAfterChangeNumber", "转容器前数量不能小于转容器后数量");
            }
        }
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            throw new ResponseException("error.SubWorkSheetNotFound", "子工单不存在");
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        if (workSheet == null) {
            throw new ResponseException("error.WorkSheetNotFound", "工单不存在");
        }
        List<SubWorkSheet> subWorkSheetList = subWorkSheetRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        String preFixNumber = subWorkSheetSerialNumber;
        // 只有一个子工单 用工单号作为固定前缀
        if (!CollectionUtils.isEmpty(subWorkSheetList) && subWorkSheetList.size() == Constants.INT_ONE) {
            preFixNumber = workSheet.getSerialNumber();
        }
        // 子工单投产数
        int subWorksheetNumber = subWorkSheet.getNumber();
        if (subWorksheetNumber == Constants.LONG_ZERO) {
            throw new ResponseException("error.SubWorkSheetNumberIsZero", "子工单投产数为0");
        }
        int currentTime = Constants.INT_ONE;
        // 查询已经存在的容器
        List<WorkSheetContainer> queryWorkSheetContainerList = workSheetContainerRepository.findBySubWorkSheetSerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO);
        if (!CollectionUtils.isEmpty(queryWorkSheetContainerList)) {
            // 计算绑定次数最大值
            int currentSaveTime = queryWorkSheetContainerList.stream().map(WorkSheetContainer::getCustom3).filter(custom3 -> !ObjectUtils.isEmpty(custom3)).map(Integer::parseInt).max(Integer::compareTo).orElse(Constants.INT_ONE);
            currentTime = currentSaveTime + 1;
        }
        Boolean isContainerChangeNumber = isContainerChangeNumber(subWorkSheet);
        if (currentTime > Constants.INT_ONE) {
            throw new ResponseException("error.ContainerChangeNumberIsTwo", "该子工单已经生成了容器");
        }
        String containerRuleConfig = getContainerRuleConfig();
        // 容器的数量 子工单投产数/容器内数量 并计算余数
        if (beforeChangeNumber == null) {
            beforeChangeNumber = (Integer) parseContainerRuleConfig(containerRuleConfig, 1, 1120);
        }
        int containerNumber = subWorksheetNumber / beforeChangeNumber;
        int remainder = subWorksheetNumber % beforeChangeNumber;
        if (remainder > Constants.LONG_ZERO) {
            containerNumber++;
        }
        if (!CollectionUtils.isEmpty(queryWorkSheetContainerList) && queryWorkSheetContainerList.size() + containerNumber > subWorksheetNumber) {
            throw new ResponseException("error.ContainerNumberIsNotMatch", "容器不能超过子工单投产数");
        }
        List<Container> containerList = Lists.newArrayList();
        // 容器列表
        for (int i = 1; i <= containerNumber; i++) {
            String containerPrefix = "";
            String code = containerPrefix + preFixNumber + "-" + String.format("%03d", i);
            generateContainer(code, subWorkSheetSerialNumber, beforeChangeNumber, remainder, i, containerNumber, subWorkSheet, currentTime, containerList);
        }
        if (isContainerChangeNumber) {
            // 容器的数量 子工单投产数/容器内数量 并计算余数
            if (afterChangeNumber == null) {
                afterChangeNumber = (Integer) parseContainerRuleConfig(containerRuleConfig, 2, 1000);
            }
            int secondContainerNumber = subWorksheetNumber / afterChangeNumber;
            int secondRemainder = subWorksheetNumber % afterChangeNumber;
            if (secondRemainder > Constants.LONG_ZERO) {
                secondContainerNumber++;
            }
            if (!CollectionUtils.isEmpty(queryWorkSheetContainerList) && queryWorkSheetContainerList.size() + containerNumber + secondContainerNumber > subWorksheetNumber) {
                throw new ResponseException("error.ContainerGenerated", "容器不能超过子工单投产数");
            }
            for (int i = 1; i <= secondContainerNumber; i++) {
                String containerPrefix = (String) parseContainerRuleConfig(containerRuleConfig, 3, "R");
                String code = containerPrefix + preFixNumber + "-" + String.format("%03d", i);
                generateContainer(code, subWorkSheetSerialNumber, afterChangeNumber, secondRemainder, i, secondContainerNumber, subWorkSheet, currentTime, containerList);
            }
        }
        List<ContainerResponseDTO> containerResponseList = MapperUtils.mapAll(containerList, ContainerResponseDTO.class);
        return containerResponseList;
    }

    /**
     * 生成容器
     *
     * @param code                     容器编码
     * @param subWorkSheetSerialNumber 子工单编码
     * @param number                   数量
     * @param remainder                余数
     * @param i                        序号
     * @param containerNumber          容器数量
     * @param subWorkSheet             子工单
     * @param currentTime              绑定次数
     * @param containerList            容器列表
     */
    private void generateContainer(String code, String subWorkSheetSerialNumber, Integer number, int remainder, int i, int containerNumber, SubWorkSheet subWorkSheet, int currentTime, List<Container> containerList) {
        Container container = new Container();
        container.setName(code).setCode(code).setStatus(Constants.FALSE).setDeleted(Constants.LONG_ZERO);
        // 子工单
        container.setCustom1(subWorkSheetSerialNumber);
        // 设置容器数量用custom2字段
        container.setCustom2(String.valueOf(number));
        if (remainder > Constants.LONG_ZERO && i == containerNumber) {
            container.setCustom2(String.valueOf(remainder));
        }
        Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        if (containerOptional.isEmpty()) {
            containerAdaptRepository.save(container);
            WorkSheetContainer workSheetContainer = new WorkSheetContainer();
            WorkSheet workSheet = Optional.of(subWorkSheet).map(SubWorkSheet::getWorkSheet).orElse(null);
            // custom1 0 为导入的容器 1 为创建的容器
            workSheetContainer.setContainer(container).setSubWorkSheet(subWorkSheet).setWorkSheet(workSheet).setCustom1(String.valueOf(Constants.INT_ZERO));
            // 容器数量
            workSheetContainer.setCustom2(String.valueOf(number));
            if (remainder > Constants.LONG_ZERO && i == containerNumber) {
                workSheetContainer.setCustom2(String.valueOf(remainder));
            }
            // 绑定次数
            workSheetContainer.setCustom3(String.valueOf(currentTime));
            workSheetContainerRepository.save(workSheetContainer);
            containerList.add(container);
        }
    }

    /**
     * 获取容器规则配置
     *
     * @return java.lang.String 容器规则配置
     */
    public String getContainerRuleConfig() {
        try {
            return ResponseDataUtils.get(dictionaryFeignClient.findDictionaryByCode("key_container_code_rule"));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取容器配置
     *
     * @param configJson   配置json
     * @param key          key
     * @param defaultValue 默认值
     * @return java.lang.Object 容器配置
     */
    public Object parseContainerRuleConfig(String configJson, int key, Object defaultValue) {
        if (ValidateUtils.isValid(configJson)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 将 JSON 字符串解析为 List<Map<String, String>>
                List<Map<String, Object>> list = objectMapper.readValue(configJson, new TypeReference<>() {
                });
                // 遍历列表，查找指定的 key
                for (Map<String, Object> map : list) {
                    // 检查当前项的key是否与目标key匹配
                    if (Objects.equals(safeObjectToString(map.get("key")), String.valueOf(key))) {
                        // 如果匹配，返回对应的value
                        return map.get("value");
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            // 如果找不到匹配的key，返回null或自定义的默认值
            return defaultValue;
        }
        return defaultValue;
    }

    /**
     * 是否转数量
     *
     * @param subWorkSheet 子工单
     * @return java.lang.Boolean 是否转数量
     */
    public Boolean isContainerChangeNumber(SubWorkSheet subWorkSheet) {
        Long pedigreeId = Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getWorkSheet).map(WorkSheet::getPedigree).map(Pedigree::getId).orElse(null);
        if (pedigreeId != null) {
            Optional<PedigreeConfig> pedigreeConfigOptional = pedigreeConfigRepository.findByPedigreeIdAndDeleted(pedigreeId, Constants.LONG_ZERO);
            if (pedigreeConfigOptional.isPresent() && pedigreeConfigOptional.get().getIsEnable() && !ObjectUtils.isEmpty(pedigreeConfigOptional.get().getCustom1())) {
                return Boolean.parseBoolean(pedigreeConfigOptional.get().getCustom1());
            }
        }
        return true;
    }

    /**
     * 校验子工单和容器
     *
     * @param containerCheckDTO 容器校验DTO
     * @return net.airuima.dto.base.BaseDTO 是否校验通过结果
     */
    public BaseDTO verifyContainer(ContainerCheckDTO containerCheckDTO) {
        // 子工单id
        Long subWorkSheetId = containerCheckDTO.getSubWorkSheetId();
        if (ObjectUtils.isEmpty(subWorkSheetId)) {
            return new BaseDTO(Constants.KO, "子工单为空");
        }
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findByIdAndDeleted(subWorkSheetId, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            return new BaseDTO(Constants.KO, "子工单不存在");
        }
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        // 容器
        String containerCode = containerCheckDTO.getContainerCode();
        if (ObjectUtils.isEmpty(containerCode)) {
            return new BaseDTO(Constants.KO, "容器编码为空");
        }
        Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(containerCode, Constants.LONG_ZERO);
        if (containerOptional.isEmpty()) {
            return new BaseDTO(Constants.KO, "容器不存在");
        }
        Container container = containerOptional.get();
        if (ObjectUtils.isEmpty(container.getCustom1())) {
            return new BaseDTO(Constants.KO, "容器未绑定子工单");
        }
        WorkSheetContainer workSheetContainer = workSheetContainerRepository.findBySubWorkSheetSerialNumberAndContainerCodeAndDeleted(subWorkSheet.getSerialNumber(), containerCode, Constants.LONG_ZERO);
        if (workSheetContainer == null) {
            return new BaseDTO(Constants.KO, "容器和子工单不匹配");
        } else {
            return new BaseDTO(Constants.OK);
        }
    }

    /**
     * 子工单容器导入
     *
     * @param file 工子工单容器导入文件
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>> 容器错误消息
     */
    public List<Map<String, Object>> importExcel(MultipartFile file) {
        // 解析 excel 文件
        List<Map<String, Object>> rowList = ExcelUtils.parseExcel(file);
        List<Map<String, Object>> illegalDataList = new ArrayList<>();
        List<Container> containerList = new ArrayList<>();
        List<WorkSheetContainer> workSheetContainerList = new ArrayList<>();
        // 对每一行数据进行处理
        rowList.forEach(row -> {
            try {
                // 获取子工单编码
                String subWorkSheetSerialNumber = safeObjectToString(row.get(SubWorkSheetExcelConstants.SUB_WORKSHEET));
                if (ObjectUtils.isEmpty(subWorkSheetSerialNumber) || "null".equals(subWorkSheetSerialNumber)) {
                    throw new ResponseException("error.SubWorkSheetSerialNumberIsNull", "子工单编码为空");
                }
                SubWorkSheet subWorkSheet = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO)
                        .orElseThrow(() -> new ResponseException("error.SubWorkSheetNotFoundError", "子工单不存在"));
                // 获取容器编码
                String containerCode = safeObjectToString(row.get(SubWorkSheetExcelConstants.CONTAINER_CODE));
                if (ObjectUtils.isEmpty(containerCode) || "null".equals(containerCode)) {
                    throw new ResponseException("error.ContainerCodeIsNull", "容器编码为空");
                }
                // 获取数量
                Integer number = Constants.INT_ZERO;
                int currentTime = Constants.INT_ONE;
                // 查询已经存在的容器
                List<WorkSheetContainer> queryWorkSheetContainerList = workSheetContainerRepository.findBySubWorkSheetSerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(queryWorkSheetContainerList)) {
                    // 计算绑定次数最大值
                    int currentSaveTime = queryWorkSheetContainerList.stream().map(WorkSheetContainer::getCustom3).filter(custom3 -> !ObjectUtils.isEmpty(custom3)).map(Integer::parseInt).max(Integer::compareTo).orElse(Constants.INT_ONE);
                    currentTime = currentSaveTime + 1;
                }
                // 转数量为2次 否则只能一次
                String containerRuleConfig = getContainerRuleConfig();
                if (currentTime == Constants.INT_ONE) {
                    number = (Integer) parseContainerRuleConfig(containerRuleConfig, 1, 1120);
                }
                if (currentTime == Constants.INT_TWO) {
                    number = (Integer) parseContainerRuleConfig(containerRuleConfig, 2, 1000);
                }
                String numberString = safeObjectToString(row.get(SubWorkSheetExcelConstants.NUMBER));
                if (!ObjectUtils.isEmpty(numberString) && !"null".equals(numberString)) {
                    number = Integer.valueOf(numberString);
                }
                // 容器数量
                if (number == Constants.LONG_ZERO) {
                    throw new ResponseException("error.ContainerNumberIsZero", "容器数量为0");
                }
                Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(containerCode, Constants.LONG_ZERO);
                if (containerOptional.isEmpty()) {
                    Container container = new Container();
                    container.setCode(containerCode).setName(containerCode).setStatus(false).setDeleted(Constants.LONG_ZERO);
                    container.setCustom1(subWorkSheetSerialNumber);
                    container.setCustom2(String.valueOf(number));
                    containerList.add(container);
                    WorkSheetContainer workSheetContainer = new WorkSheetContainer();
                    WorkSheet workSheet = Optional.ofNullable(subWorkSheet).map(SubWorkSheet::getWorkSheet).orElse(null);
                    workSheetContainer.setContainer(container).setSubWorkSheet(subWorkSheet).setWorkSheet(workSheet).setCustom1(String.valueOf(Constants.INT_ONE));
                    workSheetContainer.setCustom2(String.valueOf(number));
                    workSheetContainer.setCustom3(String.valueOf(currentTime));
                    workSheetContainerList.add(workSheetContainer);
                } else {
                    Container container = containerOptional.get();
                    if (!ObjectUtils.isEmpty(numberString) && !"null".equals(numberString)) {
                        container.setCustom2(numberString);
                        containerAdaptRepository.save(container);
                        WorkSheetContainer workSheetContainer = workSheetContainerRepository.findBySubWorkSheetSerialNumberAndContainerCodeAndDeleted(subWorkSheet.getSerialNumber(), containerCode, Constants.LONG_ZERO);
                        if (workSheetContainer != null) {
                            workSheetContainer.setCustom2(numberString);
                            workSheetContainerRepository.save(workSheetContainer);
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                row.put("错误信息", e.getMessage());
                illegalDataList.add(row);
            }
        });
        if (!CollectionUtils.isEmpty(containerList)) {
            containerAdaptRepository.saveAll(containerList);
        }
        if (!CollectionUtils.isEmpty(workSheetContainerList)) {
            workSheetContainerRepository.saveAll(workSheetContainerList);
        }
        return illegalDataList;
    }

    /**
     * 处理excel的数据转为字符串
     *
     * @param obj 对象
     * @return 字符串
     */
    public static String safeObjectToString(Object obj) {
        if (obj == null) {
            return "";
        } else if (obj instanceof Long || obj instanceof Integer) {
            return String.valueOf(obj);
        } else {
            // 对于其他所有类型，依然调用 toString()
            return obj.toString();
        }
    }

    /**
     * 根据容器编码获取容器信息
     *
     * @param code 容器编码
     * @return net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDTO 容器信息
     */
    public ContainerDTO getContainerByCode(String code) {
        Optional<Container> containerOptional = containerAdaptRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        if (containerOptional.isEmpty()) {
            return null;
        }
        Container container = containerOptional.get();
        ContainerDTO containerDTO = MapperUtils.map(container, ContainerDTO.class);
        // custom2为容器数量
        containerDTO.setNumber(ObjectUtils.isEmpty(container.getCustom2()) ? Constants.INT_ZERO : Integer.parseInt(container.getCustom2()));
        containerDTO.setSubWorkSheetSerialNumber(container.getCustom1());
        return containerDTO;
    }

}
