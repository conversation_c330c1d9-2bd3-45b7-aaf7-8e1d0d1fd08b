package net.airuima.sygd.piugin.service.procedure.batch;

import net.airuima.constant.Constants;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.repository.procedure.batch.ContainerDetailRepository;
import net.airuima.service.procedure.batch.api.IRollbackStepService;
import net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainerInfo;
import net.airuima.sygd.piugin.repository.procedure.batch.ChangeContainerInfoRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ChangeContainerRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerAdaptRepository;
import net.airuima.web.rest.procedure.batch.dto.RollBackDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Optional;

@Service
@Transactional(rollbackFor = Exception.class)
public class RollbackStepAdaptService {

    @Autowired
    private IRollbackStepService[] rollbackStepServices;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private ChangeContainerRepository changeContainerRepository;


    @Autowired
    private ContainerAdaptRepository containerAdaptRepository;

    @Autowired
    private ChangeContainerInfoRepository changeContainerInfoRepository;

    /**
     * 回退容器
     *
     * @param rollBackDto 回退参数DTO
     * @return net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO 容器详情替换DTO
     */
    public ContainerDetailReplaceDTO deleteContainerDetailById(RollBackDTO rollBackDto) {
        // 回退容器转换
        ContainerDetail containerDetail = containerDetailRepository.findByIdAndDeleted(rollBackDto.getContainerDetailId(), Constants.LONG_ZERO);
        if (containerDetail != null) {
            Container container = containerDetail.getContainer();
            WorkCell workCell = containerDetail.getWorkCell();
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
            Step step = batchWorkDetail.getStep();
            //查找容器转换记录
            ChangeContainer changeContainer = changeContainerRepository.findBySubWorkSheetIdAndWorkCellIdAndContainerIdAndStepIdAndDeleted(subWorkSheet.getId(), workCell.getId(), container.getId(), step.getId(), Constants.LONG_ZERO);
            // 如果直接查到 说明没有进行合并容器
            if (changeContainer == null) {
                //没有找到说明可能已经合并容器了 查找删除记录最大的版本记录
                List<ChangeContainer> changeContainerDoneList = changeContainerRepository.findByDeletedChangeHistory(subWorkSheet.getId(), workCell.getId(), container.getId(), step.getId());
                if (!CollectionUtils.isEmpty(changeContainerDoneList)) {
                    // 获取数值最大的版本号
                    Integer maxVersion = changeContainerDoneList.stream().map(ChangeContainer::getVersion).max(Integer::compareTo).orElse(Constants.INT_ONE);
                    // 获取版本号为maxVersion的容器转换记录
                    changeContainer = changeContainerDoneList.stream().filter(cc -> cc.getVersion().equals(maxVersion)).findFirst().orElse(null);
                }
            }
            if (changeContainer != null) {
                Optional<ChangeContainerInfo> changeContainerInfoOptional = changeContainerInfoRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
                // 转容器记录不为null 说明就是被合并的容器回退
                Container originContainer = changeContainer.getOriginContainer();
                // 更新原容器当前数量
                if (changeContainerInfoOptional.isPresent()) {
                    Integer bigNumber = changeContainerInfoOptional.get().getBigNumber();
                    if (bigNumber != null) {
                        // custom2 是当前容器数量
                        originContainer.setCustom2(String.valueOf(bigNumber));
                    }
                }
                containerAdaptRepository.save(originContainer);
                // 恢复合并前数量
                if (changeContainerInfoOptional.isPresent()) {
                    Integer smallNumber = changeContainerInfoOptional.get().getSmallNumber();
                    if (smallNumber != null) {
                        // custom2 是当前容器数量
                        container.setCustom2(String.valueOf(smallNumber));
                    }
                }
                containerAdaptRepository.save(container);
                changeContainerRepository.logicDelete(changeContainer);
            } else {
                // 转容器记录为空 判断是不是合并的那个容器
                Optional<ChangeContainerInfo> changeContainerInfoOptional = changeContainerInfoRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
                if (changeContainerInfoOptional.isPresent()) {
                    ChangeContainerInfo changeContainerInfo = changeContainerInfoOptional.get();
                    Container mergeContainer = changeContainerInfo.getMergeContainer();
                    // 判断是不是合并的那个容器
                    if (mergeContainer.getId().equals(container.getId())) {
                        // 恢复转容器的记录
                        List<ChangeContainer> changeContainerList = changeContainerRepository.findDeleteChangeContainerBySubWorkSheetId(subWorkSheet.getId());
                        if (!CollectionUtils.isEmpty(changeContainerList)) {
                            for (ChangeContainer changeContainerHistory : changeContainerList) {
                                changeContainerHistory.setDeleted(Constants.LONG_ZERO);
                                Integer remainNumber = changeContainerInfo.getRemainNumber();
                                changeContainerHistory.setRemainNumber(remainNumber);
                                changeContainerRepository.save(changeContainerHistory);
                                // 恢复被合并的容器
                                Container originContainer = changeContainerHistory.getOriginContainer();
                                //custom2为容器当前数量;
                                originContainer.setCustom2(String.valueOf(remainNumber));
                                //Custom3记录转容器前的数量
                                if (changeContainerInfo.getBigNumber() != null) {
                                    originContainer.setCustom3(String.valueOf(changeContainerInfo.getBigNumber()));
                                }
                                containerAdaptRepository.save(originContainer);
                            }
                        }
                        // 恢复被合并的容器
                        Integer lastNumber = changeContainerInfo.getLastNumber();
                        if (lastNumber != null) {
                            mergeContainer.setCustom2(String.valueOf(lastNumber));
                            mergeContainer.setCustom3(null);
                            containerAdaptRepository.save(mergeContainer);
                        }
                    }
                }
            }
        }
        ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepServices[0].deleteContainerDetailById(rollBackDto);
        // 如果是最后一个需要合并的容器回退 需要将生成回退后最新的容器生产详情status变为1 这样可以重新录入
        if (containerDetail != null) {
            Container container = containerDetail.getContainer();
            if (container.getCustom2() != null && Integer.parseInt(container.getCustom2()) < 1000) {
                containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(container.getId(), Constants.LONG_ZERO).ifPresent(c -> {
                    c.setStatus(Constants.INT_ONE);
                    containerDetailRepository.save(c);
                });
            }
            // 如果回退后 容器生产详情带流转数是0 说明合并容器了  重新赋值流转数为投产数
            containerDetailRepository.findTop1ByContainerIdAndDeletedOrderByIdDesc(container.getId(), Constants.LONG_ZERO).ifPresent(c -> {
                if (c.getTransferNumber() == 0) {
                    c.setTransferNumber(containerDetail.getInputNumber());
                }
                if (c.getStatus() == Constants.INT_ZERO) {
                    c.setStatus(Constants.INT_ONE);
                }
                containerDetailRepository.save(c);
            });
        }
        //
        return containerDetailReplaceDto;
    }
}
