package net.airuima.sygd.piugin.service.procedure;

import net.airuima.constant.Constants;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.repository.procedure.aps.WorkSheetRepository;
import net.airuima.service.CommonJpaService;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient;
import net.airuima.sygd.piugin.repository.procedure.batch.CustomWorkSheetClientRepository;
import net.airuima.sygd.piugin.util.StringArrayUtils;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单客户信息Service
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CustomWorkSheetClientService extends CommonJpaService<CustomWorkSheetClient> {

    @Autowired
    private CustomWorkSheetClientRepository customWorkSheetClientRepository;

    @Autowired
    private WorkSheetRepository workSheetRepository;

    /**
     * 保存客户信息
     *
     * @param requestWorkSheetClient 客户信息
     * @return net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient 客户信息
     */
    public CustomWorkSheetClient saveWorkSheetClient(CustomWorkSheetClient requestWorkSheetClient) {
        WorkSheet workSheet = requestWorkSheetClient.getWorkSheet();
        WorkSheet queryWorkSheet = workSheetRepository.findByIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO).orElse(null);
        if (Objects.isNull(queryWorkSheet)) {
            throw new ResponseException("error.WorkSheetNotExist", "工单不存在");
        }
        String code = requestWorkSheetClient.getCode();
        String name = requestWorkSheetClient.getName();
        String fullName = requestWorkSheetClient.getFullName();
        Long id = requestWorkSheetClient.getId();
        //新增
        if (Objects.isNull(id)) {
            Optional<CustomWorkSheetClient> workSheetClientOptional = customWorkSheetClientRepository.findByWorkSheetIdAndCodeAndDeleted(workSheet.getId(), code, Constants.LONG_ZERO);
            if (workSheetClientOptional.isPresent()) {
                throw new ResponseException("error.ClientExist", "记录已存在");
            }
            //保存客户信息
            CustomWorkSheetClient saveWorkSheetClient = new CustomWorkSheetClient();
            saveWorkSheetClient.setWorkSheet(workSheet);
            saveWorkSheetClient.setCode(code);
            saveWorkSheetClient.setName(name);
            saveWorkSheetClient.setFullName(fullName);
            saveWorkSheetClient.setDeleted(Constants.LONG_ZERO);
            customWorkSheetClientRepository.save(saveWorkSheetClient);
        } else {
            Optional<CustomWorkSheetClient> packageRuleOptional = customWorkSheetClientRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
            if (packageRuleOptional.isPresent()) {
                CustomWorkSheetClient queryCustomWorkSheetClient = packageRuleOptional.get();
                if (!Objects.equals(queryCustomWorkSheetClient.getWorkSheet().getId(), workSheet.getId()) || !Objects.equals(queryCustomWorkSheetClient.getCode(), requestWorkSheetClient.getCode())) {
                    Optional<CustomWorkSheetClient> workSheetClientOptional = customWorkSheetClientRepository.findByWorkSheetIdAndCodeAndDeleted(workSheet.getId(), code, Constants.LONG_ZERO);
                    if (workSheetClientOptional.isPresent() && !Objects.equals(workSheetClientOptional.get().getId(), id)) {
                        throw new ResponseException("error.ClientExist", "记录已存在");
                    }
                }
                customWorkSheetClientRepository.save(requestWorkSheetClient);
            }
        }

        resetWorkSheetClient(queryWorkSheet, false, null);
        return requestWorkSheetClient;
    }

    /**
     * 重置工单客户信息
     *
     * @param workSheet 工单信息
     */
    public void resetWorkSheetClient(WorkSheet workSheet, Boolean isDelete, String deleteClientCode) {
        if (Objects.isNull(workSheet)) {
            return;
        }
        List<CustomWorkSheetClient> workSheetClientList = customWorkSheetClientRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (CollectionUtils.isEmpty(workSheetClientList)) {
            if (isDelete) {
                workSheet.setCustom1(null);
                workSheetRepository.save(workSheet);
                return;
            } else {
                return;
            }
        }
        List<String> requestClientCodeList = new ArrayList<>();
        for (CustomWorkSheetClient client : workSheetClientList) {
            if (StringUtils.isNotBlank(client.getCode())) {
                String cleanClientCode = client.getCode().trim().replaceAll("\\s", "");
                if (!StringUtils.isBlank(cleanClientCode)) {
                    requestClientCodeList.add(cleanClientCode);
                }
            }
        }
        //Custom1字段
        String custom1 = workSheet.getCustom1();
        if (StringUtils.isNotBlank(custom1)) {
            List<String> dbClientList = StringArrayUtils.stringToArray(custom1.trim());
            List<String> finalList = new ArrayList<>();
            if (!isDelete) {
                // 不是删除操作，需要合并
                if (!CollectionUtils.isEmpty(dbClientList)) {
                    for (String clientCode : dbClientList) {
                        if (!finalList.contains(clientCode) && !ObjectUtils.isEmpty(clientCode)) {
                            finalList.add(clientCode);
                        }
                    }
                }
                if (!CollectionUtils.isEmpty(requestClientCodeList)) {
                    for (String requestClientCode : requestClientCodeList) {
                        if (!finalList.contains(requestClientCode) && !ObjectUtils.isEmpty(requestClientCode)) {
                            finalList.add(requestClientCode);
                        }
                    }
                }
            } else {
                if (!CollectionUtils.isEmpty(dbClientList)) {
                    for (String clientCode : dbClientList) {
                        if (!finalList.contains(clientCode) && !ObjectUtils.isEmpty(clientCode)) {
                            finalList.add(clientCode);
                        }
                    }
                }
                // 删除操作，需要删除
                if (!ObjectUtils.isEmpty(deleteClientCode)) {
                    String removeCode = deleteClientCode.trim().replaceAll("\\s", "");
                    finalList.remove(removeCode);
                }
            }
            // 客户是custom1字段
            workSheet.setCustom1(StringArrayUtils.arrayToString(finalList));
            workSheetRepository.save(workSheet);
        } else {
            custom1 = StringArrayUtils.arrayToString(requestClientCodeList);
            //客户是custom1字段
            workSheet.setCustom1(custom1);
            workSheetRepository.save(workSheet);
        }
    }

    /**
     * 删除工单客户信息
     *
     * @param ids 主键id
     */
    public void deleteEntity(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        for (Long id : ids) {
            Optional<CustomWorkSheetClient> workSheetClientOptional = customWorkSheetClientRepository.findByIdAndDeleted(id, Constants.LONG_ZERO);
            if (workSheetClientOptional.isEmpty()) {
                throw new ResponseException("error.ClientNotExist", "记录不存在");
            }
            CustomWorkSheetClient workSheetClient = workSheetClientOptional.get();
            String deleteClientCode = workSheetClient.getCode();
            WorkSheet workSheet = workSheetClient.getWorkSheet();
            customWorkSheetClientRepository.logicDelete(id);
            resetWorkSheetClient(workSheet, true, deleteClientCode);
        }
    }

    /**
     * 根据工单编号查询客户信息
     *
     * @param serialNumber 工单编号
     * @return java.util.List<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 客户信息
     */
    @Transactional(readOnly = true)
    public List<CustomWorkSheetClient> findByWorkSheet(String serialNumber) {
        if (StringUtils.isBlank(serialNumber)) {
            return Lists.newArrayList();
        }
        Optional<WorkSheet> workSheetOptional = workSheetRepository.findBySerialNumberAndDeleted(serialNumber, Constants.LONG_ZERO);
        if (workSheetOptional.isPresent()) {
            WorkSheet workSheet = workSheetOptional.get();
            return customWorkSheetClientRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        }
        return Lists.newArrayList();
    }

    /**
     * 批量保存客户信息
     *
     * @param entityList 客户信息
     */
    public void batchSaveWorkSheetClient(List<CustomWorkSheetClient> entityList) {
        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }
        for (CustomWorkSheetClient entity : entityList) {
            saveWorkSheetClient(entity);
        }
    }
}
