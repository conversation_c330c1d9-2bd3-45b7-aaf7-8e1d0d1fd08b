package net.airuima.sygd.piugin.service.procedure;

import net.airuima.client.feign.organization.StaffFeignClient;
import net.airuima.constant.Constants;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.*;
import net.airuima.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.dto.rfms.FacilityDTO;
import net.airuima.repository.procedure.aps.SubWorkSheetRepository;
import net.airuima.repository.procedure.batch.*;
import net.airuima.repository.procedure.material.WsCheckMaterialDetailRepository;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import net.airuima.sygd.piugin.repository.procedure.batch.BatchWorkDetailMaterialBatchAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.ContainerDetailAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkCellStepFacilityAdaptRepository;
import net.airuima.sygd.piugin.repository.procedure.batch.WorkSheetContainerRepository;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.*;
import net.airuima.util.ResponseException;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标签数据Service
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class LabelDataService {

    @Autowired
    private SubWorkSheetRepository subWorkSheetRepository;

    @Autowired
    private WsCheckMaterialDetailRepository wsCheckMaterialDetailRepository;

    @Autowired
    private WsStepRepository wsStepRepository;

    @Autowired
    private BatchWorkDetailRepository batchWorkDetailRepository;

    @Autowired
    private StaffFeignClient staffFeignClient;

    @Autowired
    private WorkCellStepFacilityAdaptRepository workCellStepFacilityAdaptRepository;

    @Autowired
    private BatchWorkDetailFacilityRepository batchWorkDetailFacilityRepository;

    @Autowired
    private BatchWorkDetailMaterialBatchAdaptRepository batchWorkDetailMaterialBatchAdaptRepository;

    @Autowired
    private ContainerRepository containerRepository;

    @Autowired
    private ContainerDetailAdaptRepository containerDetailAdaptRepository;

    @Autowired
    private ContainerDetailRepository containerDetailRepository;

    @Autowired
    private WorkSheetContainerRepository workSheetContainerRepository;


    /**
     * 获取标签信息
     *
     * @param labelDataQueryDTO 标签数据查询DTO
     * @return net.airuima.sygd.piugin.web.rest.procedure.batch.dto.LabelDataResponseDTO 标签数据响应DTO
     */
    @Transactional(readOnly = true)
    public LabelDataResponseDTO getLabelInfo(LabelDataQueryDTO labelDataQueryDTO) {
        String subWorkSheetSerialNumber = labelDataQueryDTO.getSubWorkSheetSerialNumber();
        Optional<SubWorkSheet> subWorkSheetOptional = subWorkSheetRepository.findBySerialNumberAndDeleted(subWorkSheetSerialNumber, Constants.LONG_ZERO);
        if (subWorkSheetOptional.isEmpty()) {
            return null;
        }
        List<String> chipList = labelDataQueryDTO.getChipList();
        SubWorkSheet subWorkSheet = subWorkSheetOptional.get();
        WorkSheet workSheet = subWorkSheet.getWorkSheet();
        LabelDataResponseDTO labelDataResponseDTO = new LabelDataResponseDTO();
        // 获取模板变量
        List<String> varList = labelDataQueryDTO.getVarList();
        if (varList.contains("WsChipBatchCode")) {
            // 获取工单的领料单
            List<WsCheckMaterialDetail> wsCheckMaterialDetailList = wsCheckMaterialDetailRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            // 遍历领料单
            String chipBatchCode = "";
            for (WsCheckMaterialDetail wsCheckMaterialDetail : wsCheckMaterialDetailList) {
                String materialCode = wsCheckMaterialDetail.getMaterialDto().getCode();
                //取物料编码 第3位开始开始匹配chipList里面字符串的批次号
                for (String chip : chipList) {
                    if (materialCode.substring(2).startsWith(chip)) {
                        chipBatchCode = Optional.ofNullable(wsCheckMaterialDetail.getBatch()).orElse("");
                        break;
                    }
                }
            }
            labelDataResponseDTO.setChipBatchCode(chipBatchCode);
        }
        if (varList.contains("ChipBatchCode")) {
            // 获取工单的领料单
            List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeletedWhenDataFilter(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(batchWorkDetailList)) {
                List<Long> batchDetailIdList = batchWorkDetailList.stream().map(BatchWorkDetail::getId).toList();
                List<BatchWorkDetailMaterialBatch> batchWorkDetailMaterialBatchList = batchWorkDetailMaterialBatchAdaptRepository.findByBatchWorkDetailIdInAndDeleted(batchDetailIdList, Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(batchWorkDetailMaterialBatchList)) {
                    // 遍历生产详情批次
                    String chipBatchCode = "";
                    for (BatchWorkDetailMaterialBatch batchWorkDetailMaterialBatch : batchWorkDetailMaterialBatchList) {
                        String materialCode = batchWorkDetailMaterialBatch.getMaterialDto().getCode();
                        //取物料编码 第3位开始开始匹配chipList里面字符串的批次号
                        for (String chip : chipList) {
                            if (materialCode.substring(2).startsWith(chip)) {
                                chipBatchCode = Optional.ofNullable(batchWorkDetailMaterialBatch.getMaterialBatch()).orElse("");
                                break;
                            }
                        }
                    }
                    labelDataResponseDTO.setChipBatchCode(chipBatchCode);
                }

            }

        }
        if (varList.contains("StoreInCheckUserName")) {
            // 查找入库检操作人
            List<WsStep> wsStepList = wsStepRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
            // 查找名字有入库检的工序
            List<Step> storeStepList = Lists.newArrayList();
            List<Step> stepList = Lists.newArrayList();
            for (WsStep wsStep : wsStepList) {
                if (wsStep.getStep().getName().contains("入库检")) {
                    storeStepList.add(wsStep.getStep());
                }
                stepList.add(wsStep.getStep());
            }
            labelDataResponseDTO.setStoreInCheckUserName("");
            if (!CollectionUtils.isEmpty(storeStepList)) {
                List<Long> stepIdList = storeStepList.stream().map(Step::getId).toList();
                List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndStepIdInAndDeleted(subWorkSheet.getId(), stepIdList, Constants.LONG_ZERO);
                if (!CollectionUtils.isEmpty(batchWorkDetailList)) {
                    Optional<BatchWorkDetail> batchWorkDetailOptional = batchWorkDetailList.stream().findFirst();
                    BatchWorkDetail batchWorkDetail = batchWorkDetailOptional.get();
                    Long operatorId = batchWorkDetail.getOperatorId();
                    if (operatorId != null) {
                        StaffDTO staffDTO = staffFeignClient.findById(operatorId);
                        labelDataResponseDTO.setStoreInCheckUserName(Optional.ofNullable(staffDTO).map(StaffDTO::getName).orElse(""));
                    }
                }
            }
        }
        if (varList.contains("SealMachine") || varList.contains("EutecticMachine")) {
            // 查找设备
            labelDataResponseDTO.setMachineList(Lists.newArrayList());
            List<BatchWorkDetail> batchWorkDetailList = batchWorkDetailRepository.findBySubWorkSheetIdAndDeleted(subWorkSheet.getId(), Constants.LONG_ZERO);
            if (!CollectionUtils.isEmpty(batchWorkDetailList)) {
                List<Long> batchWorkDetailListIdList = batchWorkDetailList.stream().map(BatchWorkDetail::getId).toList();
                if (!CollectionUtils.isEmpty(batchWorkDetailListIdList)) {
                    List<FacilityDTO> facilityList = new ArrayList<>();
                    for (Long batchWorkDetailId : batchWorkDetailListIdList) {
                        List<BatchWorkDetailFacility> batchWorkDetailFacilityList = batchWorkDetailFacilityRepository.findByBatchWorkDetailIdAndDeleted(batchWorkDetailId, Constants.LONG_ZERO);
                        if (!CollectionUtils.isEmpty(batchWorkDetailFacilityList)) {
                            List<FacilityDTO> facilityDTOList = batchWorkDetailFacilityList.stream().map(BatchWorkDetailFacility::getFacilityDto).filter(Objects::nonNull).toList();
                            if (!CollectionUtils.isEmpty(facilityDTOList)) {
                                facilityList.addAll(facilityDTOList);
                            }
                        }
                    }
                    if (!CollectionUtils.isEmpty(facilityList)) {
                        List<MachineDTO> machineDTOList = facilityList.stream().map(f -> new MachineDTO(f.getId(), f.getName(), f.getCode())).distinct().toList();
                        labelDataResponseDTO.setMachineList(machineDTOList);
                    }
                }
            }
        }
        return labelDataResponseDTO;
    }

    /**
     * 获取容器信息
     *
     * @param containerDataQueryDTO 容器数据查询DTO
     * @return net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDataDTO 容器数据DTO
     */
    public ContainerDataDTO getContainer(ContainerDataQueryDTO containerDataQueryDTO) {
        String code = containerDataQueryDTO.getCode();
        Boolean bind = containerDataQueryDTO.getBind();
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        ContainerDataDTO containerDataDTO = new ContainerDataDTO();
        if (containerOptional.isEmpty()) {
            return null;
        }
        Container container = containerOptional.get();
        BeanUtils.copyProperties(container, containerDataDTO);
        Optional<ContainerDetail> containerDetailOptional = Optional.empty();
        if (bind == null) {
            containerDetailOptional = containerDetailAdaptRepository.findTop1ByContainerCodeAndDeletedOrderByIdDesc(code, Constants.LONG_ZERO);
        } else {
            Integer status = Constants.INT_ZERO;
            if (bind) {
                status = Constants.INT_ONE;
            }
            containerDetailOptional = containerDetailRepository.findTop1ByContainerCodeAndStatusAndDeletedOrderByIdDesc(code, status, Constants.LONG_ZERO);
        }
        if (containerDetailOptional.isPresent()) {
            ContainerDetail containerDetail = containerDetailOptional.get();
            ContainerDetailDataDTO containerDetailDataDTO = new ContainerDetailDataDTO();
            // 容器详情
            BeanUtils.copyProperties(containerDetail, containerDetailDataDTO);
            containerDataDTO.setContainerDetail(containerDetailDataDTO);
            BatchWorkDetail batchWorkDetail = containerDetail.getBatchWorkDetail();
            if (batchWorkDetail != null) {
                SubWorkSheet subWorkSheet = batchWorkDetail.getSubWorkSheet();
                if (subWorkSheet != null) {
                    containerDataDTO.setSubWorkSheet(subWorkSheet);
                }
            }
        }
        return containerDataDTO;
    }

    /**
     * 获取打印容器信息
     *
     * @param containerDataQueryDTO 容器数据查询DTO
     * @return net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDataDTO 容器数据DTO
     */
    public ContainerDataDTO getPrintContainer(ContainerDataQueryDTO containerDataQueryDTO) {
        String code = containerDataQueryDTO.getCode();
        Optional<Container> containerOptional = containerRepository.findByCodeAndDeleted(code, Constants.LONG_ZERO);
        ContainerDataDTO containerDataDTO = new ContainerDataDTO();
        Container container = containerOptional.get();
        BeanUtils.copyProperties(container, containerDataDTO);
        Optional<WorkSheetContainer> workSheetContainerOptional = workSheetContainerRepository.findByContainerCodeAndDeleted(code, Constants.LONG_ZERO);
        if (workSheetContainerOptional.isPresent()) {
            WorkSheetContainer workSheetContainer = workSheetContainerOptional.get();
            SubWorkSheet subWorkSheet = workSheetContainer.getSubWorkSheet();
            containerDataDTO.setSubWorkSheet(subWorkSheet);
        }
        return containerDataDTO;
    }
}
