package net.airuima.sygd.piugin.service.rworker.material.impl;

import net.airuima.client.feign.rule.SystemCodeFeignClient;
import net.airuima.constant.Constants;
import net.airuima.constant.ConstantsEnum;
import net.airuima.constant.WsEnum;
import net.airuima.domain.base.pedigree.PedigreeStepMaterialRule;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.BatchWorkDetail;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.domain.procedure.batch.WsMaterial;
import net.airuima.domain.procedure.batch.WsStep;
import net.airuima.domain.procedure.single.SnWorkDetail;
import net.airuima.dto.process.StepDTO;
import net.airuima.repository.base.process.WorkFlowRepository;
import net.airuima.repository.procedure.batch.WsMaterialRepository;
import net.airuima.repository.procedure.batch.WsStepRepository;
import net.airuima.service.common.CommonService;
import net.airuima.service.rworker.material.IMaterialService;
import net.airuima.service.rworker.material.impl.MaterialServiceImpl;
import net.airuima.util.*;
import net.airuima.web.rest.rworker.material.dto.RworkerFeedingMaterialBaseDTO;
import net.airuima.web.rest.rworker.process.dto.RworkerFeedingMaterialRuleGetDTO;
import net.airuima.web.rest.rworker.process.dto.general.MaterialSaveInfo;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2024, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
@Order(-1)
public class MaterialAdaptServiceImpl implements IMaterialService {
    @Autowired
    private WsStepRepository wsStepRepository;
    @Autowired
    private CommonService commonService;

    @Autowired
    private WorkFlowRepository workFlowRepository;

    @Autowired
    private SystemCodeFeignClient systemCodeFeignClient;

    @Autowired
    private WsMaterialRepository wsMaterialRepository;

    @Override
    public RworkerFeedingMaterialRuleGetDTO findBaseFeedingMaterialInfo(Boolean onlineMaintainOrTransferWorkFlow, SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, Long workFlowId, RworkerFeedingMaterialBaseDTO rworkerFeedingMaterialBaseDTO) {
        RworkerFeedingMaterialRuleGetDTO rworkerFeedingMaterialRuleGetDTO = findBaseFeedingMaterialInfoBase(onlineMaintainOrTransferWorkFlow, subWorkSheet, workSheet, step, workFlowId, rworkerFeedingMaterialBaseDTO);
        WsStep currentWsStep = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO)
                .orElse(wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null)) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        WorkFlow snapshotWorkFlow = null != currentWsStep && null != currentWsStep.getWorkFlow() ? currentWsStep.getWorkFlow() : workSheet.getWorkFlow();
        long snapshotWorkFlowId = Objects.nonNull(workFlowId) ? workFlowId : snapshotWorkFlow.getId();
        //获取工序上料规则
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(), workSheet.getPedigree().getId(), snapshotWorkFlowId, step.getId());
        //如果上料规则比
        if (!CollectionUtils.isEmpty(pedigreeStepMaterialRuleList) && (Objects.isNull(rworkerFeedingMaterialBaseDTO)
                || ((rworkerFeedingMaterialRuleGetDTO.getFeedingMaterialBatchRuleInfoList().size() + rworkerFeedingMaterialRuleGetDTO.getFeedingSnRuleInfoList().size()) < pedigreeStepMaterialRuleList.size()))) {
            List<RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo> feedingMaterialBatchRuleInfoList = Lists.newArrayList();
            List<RworkerFeedingMaterialRuleGetDTO.FeedingSnRuleInfo> feedingSnRuleInfoList = Lists.newArrayList();
            pedigreeStepMaterialRuleList.forEach(pedigreeStepMaterialRule -> {
                if (pedigreeStepMaterialRule.getControlMaterialGranularity() == ConstantsEnum.MATERIAL_RULE_BATCH.getCategoryName()) {
                    RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo feedingMaterialRuleInfo = new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo(pedigreeStepMaterialRule);
                    List<RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo> materialGroupInfoList = Lists.newArrayList();
                    RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo wsMaterialGroupInfo = new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo();
                    wsMaterialGroupInfo.setId(pedigreeStepMaterialRule.getMaterialId()).setCode(pedigreeStepMaterialRule.getMaterialDto().getCode())
                            .setName(pedigreeStepMaterialRule.getMaterialDto().getName()).setMaterialCategory(pedigreeStepMaterialRule.getMaterialDto().getMaterialCategory())
                            .setIsReplaceMaterial(Boolean.FALSE);
                    materialGroupInfoList.add(wsMaterialGroupInfo);
                    feedingMaterialRuleInfo.setWsMaterialGroupInfoList(materialGroupInfoList);
                    feedingMaterialRuleInfo.setWsWorkCellMaterialGroupInfoList(MapperUtils.mapAll(materialGroupInfoList, RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsWorkCellMaterialGroupInfo.class));
                    feedingMaterialBatchRuleInfoList.add(feedingMaterialRuleInfo);
                } else {
                    feedingSnRuleInfoList.add(new RworkerFeedingMaterialRuleGetDTO.FeedingSnRuleInfo(pedigreeStepMaterialRule));
                }
            });
            return new RworkerFeedingMaterialRuleGetDTO(feedingMaterialBatchRuleInfoList, feedingSnRuleInfoList);
        }
        return rworkerFeedingMaterialRuleGetDTO;
    }

    /**
     * 获取工序上料基础物料信息
     *
     * @param workSheet                     生产工单
     * @param step                          工序
     * @param rworkerFeedingMaterialBaseDTO 基础通用信息
     * @return StepFeedingMaterialRule 上料规则集合
     */
    public RworkerFeedingMaterialRuleGetDTO findBaseFeedingMaterialInfoBase(Boolean onlineMaintainOrTransferWorkFlow, SubWorkSheet subWorkSheet, WorkSheet workSheet, Step step, Long workFlowId, RworkerFeedingMaterialBaseDTO rworkerFeedingMaterialBaseDTO) {
        List<RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo> feedingMaterialBatchRuleInfoList = Lists.newArrayList();
        List<RworkerFeedingMaterialRuleGetDTO.FeedingSnRuleInfo> feedingSnRuleInfoList = Lists.newArrayList();
        //获取定制工序中工艺路线
        WsStep currentWsStep = Objects.nonNull(subWorkSheet) ? wsStepRepository.findBySubWorkSheetIdAndStepIdAndDeleted(subWorkSheet.getId(), step.getId(), Constants.LONG_ZERO)
                .orElse(wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null)) :
                wsStepRepository.findByWorkSheetIdAndStepIdAndDeleted(workSheet.getId(), step.getId(), Constants.LONG_ZERO).orElse(null);
        WorkFlow snapshotWorkFlow = null != currentWsStep && null != currentWsStep.getWorkFlow() ? currentWsStep.getWorkFlow() : workSheet.getWorkFlow();
        //单支在线返时可能没有工序快照，那么取获取工序配置
        StepDTO stepDTO = null;
        if (Objects.isNull(currentWsStep)) {
            stepDTO = commonService.findPedigreeStepConfig(workSheet.getClientId(), workSheet.getPedigree(),
                    Objects.nonNull(workFlowId) ? workFlowRepository.getReferenceById(workFlowId) : snapshotWorkFlow, step);
        }
        //当前投产是否是在线返工或者转工艺了
        boolean isOnlineMaintainOrTransferWorkFlow = Objects.isNull(currentWsStep) || (Objects.nonNull(workFlowId) && !workSheet.getWorkFlow().getId().equals(workFlowId));
        if (Objects.isNull(stepDTO) && Objects.isNull(currentWsStep)) {
            return null;
        }
        boolean controlMaterial = Objects.nonNull(stepDTO) ? stepDTO.getIsControlMaterial() : currentWsStep.getIsControlMaterial();
        //不管控物料则直接返回
        if (!controlMaterial) {
            return null;
        }
        //若是单支在线返、返工单返修或者转工艺后的工序根据系统配置来决定是否只验证是否为投料单里的物料
        if (isOnlineMaintainOrTransferWorkFlow || (Objects.nonNull(onlineMaintainOrTransferWorkFlow) && onlineMaintainOrTransferWorkFlow)) {
            String onlineMaintainMaterialControl = ResponseDataUtils.get(systemCodeFeignClient.findByCode(Constants.KEY_TRANSFER_MAINTAIN_MATERIAL_CONTROL));
            if (StringUtils.isNotBlank(onlineMaintainMaterialControl) && Integer.parseInt(onlineMaintainMaterialControl) != Constants.INT_ZERO) {
                return null;
            }
        }
        if (workSheet.getCategory() == WsEnum.ONLINE_RE_WS.getCategory() || workSheet.getCategory() == ConstantsEnum.WORK_SHEET_OFFLINE_CATEGORY.getCategoryName()) {
            String onlineMaintainMaterialControl = workSheet.getCategory() == ConstantsEnum.WORK_SHEET_ONLINE_CATEGORY.getCategoryName() ? ResponseDataUtils.get(systemCodeFeignClient.findByCode(Constants.KEY_ONLINE_MAINTAIN_MATERIAL_CONTROL)) : ResponseDataUtils.get(systemCodeFeignClient.findByCode(Constants.KEY_OFFLINE_MAINTAIN_MATERIAL_CONTROL));
            if (StringUtils.isNotBlank(onlineMaintainMaterialControl) && Integer.parseInt(onlineMaintainMaterialControl) != Constants.INT_ZERO) {
                return null;
            }
        }
        long snapshotWorkFlowId = Objects.nonNull(workFlowId) ? workFlowId : snapshotWorkFlow.getId();
        //获取工序上料规则
        List<PedigreeStepMaterialRule> pedigreeStepMaterialRuleList = commonService.findPedigreeStepMaterialRule(workSheet.getClientId(), workSheet.getPedigree().getId(), snapshotWorkFlowId, step.getId());
        if (!ValidateUtils.isValid(pedigreeStepMaterialRuleList)) {
            throw new ResponseException("error.notExistStepMaterialRule", "工序(" + step.getName() + ")需要管控物料,但尚未配置工序上料规则");
        }
        //获取工单投料单快照
        List<WsMaterial> wsMaterialList = wsMaterialRepository.findByWorkSheetIdAndDeleted(workSheet.getId(), Constants.LONG_ZERO);
        if (!ValidateUtils.isValid(wsMaterialList)) {
            throw new ResponseException("error.notExistWsMaterial", "工序(" + step.getName() + ")需要管控物料,但工单投料单不存在");
        }
        //如果物料对应的主料在投料单里不存在则去掉
        // 查找有效的主料 materialId == originMaterialId
        List<Long> validMainMaterialIds = wsMaterialList.stream().filter(wsMaterial -> wsMaterial.getMaterialId().equals(wsMaterial.getOriginMaterialId()))
                .map(WsMaterial::getMaterialId).toList();
        if (!CollectionUtils.isEmpty(validMainMaterialIds)) {
            // originMaterialId一定要在主料里面
            wsMaterialList = wsMaterialList.stream().filter(wsMaterial -> validMainMaterialIds.contains(wsMaterial.getOriginMaterialId())).toList();
        }
        rworkerFeedingMaterialBaseDTO.setWsMaterialList(wsMaterialList).setPedigreeStepMaterialRuleList(pedigreeStepMaterialRuleList);
        //过滤出投料单与上料规则交集的上料规则列表
        Map<Long, List<WsMaterial>> groupWsMaterialMap = wsMaterialList.stream().collect(Collectors.groupingBy(WsMaterial::getOriginMaterialId));
        AtomicReference<Boolean> matchedNotMainMaterial = new AtomicReference<>(Boolean.FALSE);
        pedigreeStepMaterialRuleList.forEach(pedigreeStepMaterialRule -> groupWsMaterialMap.forEach((originMaterialId, wsMaterials) -> {
            //原始物料与替代料只要有一个物料和上料规则中的物料匹配则填充物料组信息
            if (wsMaterials.stream().anyMatch(wsMaterial -> wsMaterial.getMaterialId().equals(pedigreeStepMaterialRule.getMaterialId()))) {
                if (pedigreeStepMaterialRule.getControlMaterialGranularity() == ConstantsEnum.MATERIAL_RULE_BATCH.getCategoryName()) {
                    if (pedigreeStepMaterialRule.getMaterialDto().getMaterialCategory() == ConstantsEnum.MATERIAL_NOT_MAIN_CATEGORY.getCategoryName()) {
                        matchedNotMainMaterial.set(Boolean.TRUE);
                    }
                    RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo feedingMaterialRuleInfo = new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo(pedigreeStepMaterialRule);
                    List<RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo> materialGroupInfoList = Lists.newArrayList();
                    wsMaterials.forEach(wsMaterial -> materialGroupInfoList.add(new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo(wsMaterial)));
                    feedingMaterialRuleInfo.setWsMaterialGroupInfoList(materialGroupInfoList);
                    feedingMaterialRuleInfo.setWsWorkCellMaterialGroupInfoList(MapperUtils.mapAll(materialGroupInfoList, RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsWorkCellMaterialGroupInfo.class));
                    feedingMaterialBatchRuleInfoList.add(feedingMaterialRuleInfo);
                } else {
                    feedingSnRuleInfoList.add(new RworkerFeedingMaterialRuleGetDTO.FeedingSnRuleInfo(pedigreeStepMaterialRule));
                }
            }
        }));
        //对于辅料优先取投料单和上料规则的交集，否则取上料规则的辅料来上料
        if (!matchedNotMainMaterial.get() && pedigreeStepMaterialRuleList.stream().anyMatch(pedigreeStepMaterialRule -> pedigreeStepMaterialRule.getMaterialDto().getMaterialCategory() == ConstantsEnum.MATERIAL_NOT_MAIN_CATEGORY.getCategoryName())) {
            pedigreeStepMaterialRuleList.stream().filter(pedigreeStepMaterialRule -> pedigreeStepMaterialRule.getMaterialDto().getMaterialCategory() == ConstantsEnum.MATERIAL_NOT_MAIN_CATEGORY.getCategoryName()).collect(Collectors.toList()).forEach(pedigreeStepMaterialRule -> {
                if (pedigreeStepMaterialRule.getControlMaterialGranularity() == ConstantsEnum.MATERIAL_RULE_BATCH.getCategoryName()) {
                    RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo feedingMaterialRuleInfo = new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo(pedigreeStepMaterialRule);
                    List<RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo> materialGroupInfoList = Collections.singletonList(new RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsMaterialGroupInfo(pedigreeStepMaterialRule));
                    feedingMaterialRuleInfo.setWsMaterialGroupInfoList(materialGroupInfoList);
                    feedingMaterialRuleInfo.setWsWorkCellMaterialGroupInfoList(MapperUtils.mapAll(materialGroupInfoList, RworkerFeedingMaterialRuleGetDTO.FeedingMaterialBatchRuleInfo.WsWorkCellMaterialGroupInfo.class));
                    feedingMaterialBatchRuleInfoList.add(feedingMaterialRuleInfo);
                }
            });
        }
        if (!matchedNotMainMaterial.get() && CollectionUtils.isEmpty(feedingMaterialBatchRuleInfoList) && CollectionUtils.isEmpty(feedingSnRuleInfoList)) {
            throw new ResponseException("error.notExistMatchedStepMaterialRule", "工序(" + step.getName() + ")需要管控物料,但上料规则和投料单无任何物料相匹配");
        }
        return new RworkerFeedingMaterialRuleGetDTO(feedingMaterialBatchRuleInfoList, feedingSnRuleInfoList);
    }

    @Override
    public void saveBatchWorkDetailMaterialInfo(BatchWorkDetail batchWorkDetail, List<MaterialSaveInfo> materialInfoList, int finishNumber) {
        List<MaterialSaveInfo> materialSaveInfos = materialInfoList.stream().filter(materialSaveInfo -> StringUtils.isNotBlank(materialSaveInfo.getBatch())).toList();
        BeanUtil.getBean(MaterialServiceImpl.class).saveBatchWorkDetailMaterialInfo(batchWorkDetail, materialSaveInfos, finishNumber);
    }


    @Override
    public void saveContainerWorkDetailMaterialInfo(ContainerDetail containerDetail, List<MaterialSaveInfo> materialInfoList) {
        List<MaterialSaveInfo> materialSaveInfos = materialInfoList.stream().filter(materialSaveInfo -> StringUtils.isNotBlank(materialSaveInfo.getBatch())).toList();
        BeanUtil.getBean(MaterialServiceImpl.class).saveContainerWorkDetailMaterialInfo(containerDetail, materialSaveInfos);
    }

    @Override
    public void saveSnWorkDetailMaterialInfo(SnWorkDetail snWorkDetail, List<MaterialSaveInfo> materialInfoList) {
        List<MaterialSaveInfo> materialSaveInfos = materialInfoList.stream().filter(materialSaveInfo -> StringUtils.isNotBlank(materialSaveInfo.getBatch())).toList();
        BeanUtil.getBean(MaterialServiceImpl.class).saveSnWorkDetailMaterialInfo(snWorkDetail, materialSaveInfos);
    }
}
