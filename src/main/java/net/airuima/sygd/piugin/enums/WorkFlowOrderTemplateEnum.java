package net.airuima.sygd.piugin.enums;

import java.util.HashMap;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 模板变量枚举
 *
 * <AUTHOR>
 * @date 2023-03-09
 */
public enum WorkFlowOrderTemplateEnum {

    /**
     * 模板变量枚举
     */
    PEDIGREE_CODE("产品谱系编码", "${pedigree_code}"),
    PEDIGREE_NAME("产品谱系名称", "${pedigree_name}"),
    PEDIGREE_SPECIFICATION("产品谱系规格", "${pedigree_specification}"),
    MAKER("制单人", "${maker}"),
    WORKSHEET_CREATE_TIME("下单时间", "${worksheet_create_time}"),
    SUB_WORKSHEET("子工单", "${sub_worksheet}"),
    SUB_WORKSHEET_NUMBER("子工单数", "${sub_worksheet_number}"),
    PEDIGREE_SPLIT_NUMBER("分单数量", "${pedigree_split_number}"),
    WS_MATERIAL_NAME("领料单产品名称", "${ws_material_name}"),
    WS_MATERIAL_CODE("领料单产品编码", "${ws_material_code}"),
    WS_MATERIAL_BATCH("领料单产品批次号", "${ws_material_batch}"),
    WS_MATERIAL_SPECIFICATION("领料单产品规格型号", "${ws_material_specification}"),
    STEP_NAME("工序名称", "${step_name}"),
    STEP_CODE("工序编码", "${step_code}"),
    FACILITY_NAME("设备名称", "${facility_name}"),
    STEP_NUMBER("工序投入数", "${step_number}"),
    STEP_QUALIFIED_NUMBER("工序合格数", "${step_qualified_number}"),
    STEP_UNQUALIFIED_NUMBER("工序不合格数", "${step_unqualified_number}"),
    STEP_UNQUALIFIED_NAME("工序不良名称", "${step_unqualified_name}"),
    STEP_UNQUALIFIED_ITEM("工序不良项", "${step_unqualified_item}"),
    STEP_UNQUALIFIED_DATE("工序不良日期", "${step_unqualified_date}"),
    STEP_UNQUALIFIED_CODE("工序不良编码", "${step_unqualified_code}"),
    STEP_STEP_OPERATOR_NAME("工序操作人", "${step_operator_name}"),
    ;


    WorkFlowOrderTemplateEnum(String name, String templateString) {
        this.name = name;
        this.templateString = templateString;
    }

    private static final Map<String, WorkFlowOrderTemplateEnum> enumNameMap = new HashMap<>();

    private static final Map<String, WorkFlowOrderTemplateEnum> enumTemplateMap = new HashMap<>();

    static {
        for (WorkFlowOrderTemplateEnum config : WorkFlowOrderTemplateEnum.values()) {
            enumNameMap.put(config.getName(), config);
        }
        for (WorkFlowOrderTemplateEnum config : WorkFlowOrderTemplateEnum.values()) {
            enumTemplateMap.put(config.getTemplateString(), config);
        }
    }

    public static WorkFlowOrderTemplateEnum getByName(String name) {
        return enumNameMap.get(name);
    }

    public static WorkFlowOrderTemplateEnum getByTemplateString(String templateString) {
        return enumTemplateMap.get(templateString);
    }

    public static Map<String, WorkFlowOrderTemplateEnum> getTemplateStringMap() {
        return enumTemplateMap;
    }


    public String getTemplateString() {
        return templateString;
    }

    public String getName() {
        return name;
    }

    /**
     * 动态模板
     */
    private final String templateString;

    /**
     * 名字
     */
    private final String name;
}
