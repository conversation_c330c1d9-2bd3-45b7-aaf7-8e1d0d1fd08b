package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.bom.MaterialDTO;
import net.airuima.dto.organization.OrganizationDTO;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.web.rest.procedure.wip.dto.WipInventoryDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水物料批次验证数据
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Schema(description = "胶水物料批次验证数据DTO")
public class GlueMaterialBatchVerifyDataDTO {

    @Schema(description = "胶水配置")
    private GlueConfigDTO glueConfig;


    /**
     * 物料批次
     */
    @Schema(description = "物料批次")
    private String materialBatch;

    /**
     * 入库批次
     */
    @Schema(description = "物料批次")
    private String deliverBatch;

    /**
     * 配胶毛重
     */
    @Schema(description = "配胶毛重")
    private BigDecimal grossWeight;

    /**
     * 配胶净重量
     */
    @Schema(description = "配胶净重量")
    private BigDecimal netWeight;


    /**
     * 使用部门id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "使用部门id")
    private Long organizationId;


    @Schema(description = "部门DTO")
    private OrganizationDTO organizationDto = new OrganizationDTO();

    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "线边仓id")
    private Long wipWarehouseId;


    private WipInventoryDTO.WipWarehouseDTO wipWarehouseDto = new WipInventoryDTO.WipWarehouseDTO();

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    private Long materialId;


    private MaterialDTO materialDto = new MaterialDTO();


    /**
     * 调胶员工ID
     */
    @Schema(description = " 调胶员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;


    /**
     * 调胶员工DTO
     */

    private StaffDTO operatorDto = new StaffDTO();


    /**
     * 调胶时间
     */
    @Schema(description = "调胶时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime recordDate;

    /**
     * 失效时间
     */
    @Schema(description = "失效时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireDate;

    /**
     * 状态(0:未领用;1:已领用;2:已回收)
     */
    @Schema(description = "状态(0:未领用;1:已领用;2:已回收)")

    private Integer status;

    /**
     * 领用员工ID
     */
    @Schema(description = " 领用员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long receiverId;


    /**
     * 领用员工DTO
     */
    private StaffDTO receiverDto = new StaffDTO();


    /**
     * 领用时间
     */
    @Schema(description = "领用时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime receiveDate;

    /**
     * 回收员工ID：将胶水返回到胶水房的人
     */
    @Schema(description = " 回收员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long recoverierId;

    /**
     * 回收配胶员工ID：将胶水返回的人
     */
    @Schema(description = "回收配胶员工ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long gluingRecoverierId;

    /**
     * 回收配胶员工ID
     */
    private StaffDTO gluingRecoverierDto = new StaffDTO();

    /**
     * 回收净重
     */
    @Schema(description = "回收净重")
    private BigDecimal recycleNetWeight;


    /**
     * 领用员工DTO
     */
    private StaffDTO recoverierDto = new StaffDTO();


    /**
     * 回收时间
     */
    @Schema(description = "回收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime recoveryTime;

    /**
     * 理论回收时间
     */
    @Schema(description = "理论回收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime theoreticalRecoveryTime;

    /**
     * 回温时间
     */
    @Schema(description = "回温时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime rewarmingTime;

    /**
     * 存储有效期
     */
    @Schema(description = "存储有效期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime storageExpireTime;

    /**
     * 脱泡时长
     */
    @Schema(description = "脱泡时长")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deaerationTime;


    /**
     * 质量保质期
     */
    @Schema(description = "质量保质期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime qualityExpireTime;

    /**
     * 生产日期
     */
    @Schema(description = "生产日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime productionTime;

    /**
     * 最大回温次数
     */
    @Schema(description = "最大回温次数")
    private Integer maxRewarmingTimes;


    /**
     * 回温次数
     */
    @Schema(description = "回温次数")
    private Integer rewarmingTimes;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    /**
     * 当前胶水步骤Id
     */
    @Schema(description = "当前胶水步骤Id")
    private GlueStepDTO currentGlueStep;
    /**
     * 下一步胶水步骤Id
     */
    @Schema(description = "下一步胶水步骤Id")
    private GlueStepDTO afterGlueStep;
    /**
     * 胶水流程Id
     */
    @Schema(description = "胶水流程Id")
    private GlueWorkFlowDTO glueWorkFlow;

    public GlueConfigDTO getGlueConfig() {
        return glueConfig;
    }

    public GlueMaterialBatchVerifyDataDTO setGlueConfig(GlueConfigDTO glueConfig) {
        this.glueConfig = glueConfig;
        return this;
    }

    public String getMaterialBatch() {
        return materialBatch;
    }

    public GlueMaterialBatchVerifyDataDTO setMaterialBatch(String materialBatch) {
        this.materialBatch = materialBatch;
        return this;
    }

    public String getDeliverBatch() {
        return deliverBatch;
    }

    public GlueMaterialBatchVerifyDataDTO setDeliverBatch(String deliverBatch) {
        this.deliverBatch = deliverBatch;
        return this;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public GlueMaterialBatchVerifyDataDTO setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
        return this;
    }

    public BigDecimal getNetWeight() {
        return netWeight;
    }

    public GlueMaterialBatchVerifyDataDTO setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
        return this;
    }

    public Long getOrganizationId() {
        return organizationId;
    }

    public GlueMaterialBatchVerifyDataDTO setOrganizationId(Long organizationId) {
        this.organizationId = organizationId;
        return this;
    }

    public OrganizationDTO getOrganizationDto() {
        return organizationDto;
    }

    public GlueMaterialBatchVerifyDataDTO setOrganizationDto(OrganizationDTO organizationDto) {
        this.organizationDto = organizationDto;
        return this;
    }

    public Long getWipWarehouseId() {
        return wipWarehouseId;
    }

    public GlueMaterialBatchVerifyDataDTO setWipWarehouseId(Long wipWarehouseId) {
        this.wipWarehouseId = wipWarehouseId;
        return this;
    }

    public WipInventoryDTO.WipWarehouseDTO getWipWarehouseDto() {
        return wipWarehouseDto;
    }

    public GlueMaterialBatchVerifyDataDTO setWipWarehouseDto(WipInventoryDTO.WipWarehouseDTO wipWarehouseDto) {
        this.wipWarehouseDto = wipWarehouseDto;
        return this;
    }

    public Long getMaterialId() {
        return materialId;
    }

    public GlueMaterialBatchVerifyDataDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public GlueMaterialBatchVerifyDataDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public GlueMaterialBatchVerifyDataDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public GlueMaterialBatchVerifyDataDTO setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    public LocalDateTime getRecordDate() {
        return recordDate;
    }

    public GlueMaterialBatchVerifyDataDTO setRecordDate(LocalDateTime recordDate) {
        this.recordDate = recordDate;
        return this;
    }

    public LocalDateTime getExpireDate() {
        return expireDate;
    }

    public GlueMaterialBatchVerifyDataDTO setExpireDate(LocalDateTime expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public Integer getStatus() {
        return status;
    }

    public GlueMaterialBatchVerifyDataDTO setStatus(Integer status) {
        this.status = status;
        return this;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public GlueMaterialBatchVerifyDataDTO setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
        return this;
    }

    public StaffDTO getReceiverDto() {
        return receiverDto;
    }

    public GlueMaterialBatchVerifyDataDTO setReceiverDto(StaffDTO receiverDto) {
        this.receiverDto = receiverDto;
        return this;
    }

    public LocalDateTime getReceiveDate() {
        return receiveDate;
    }

    public GlueMaterialBatchVerifyDataDTO setReceiveDate(LocalDateTime receiveDate) {
        this.receiveDate = receiveDate;
        return this;
    }

    public Long getRecoverierId() {
        return recoverierId;
    }

    public GlueMaterialBatchVerifyDataDTO setRecoverierId(Long recoverierId) {
        this.recoverierId = recoverierId;
        return this;
    }

    public Long getGluingRecoverierId() {
        return gluingRecoverierId;
    }

    public GlueMaterialBatchVerifyDataDTO setGluingRecoverierId(Long gluingRecoverierId) {
        this.gluingRecoverierId = gluingRecoverierId;
        return this;
    }

    public StaffDTO getGluingRecoverierDto() {
        return gluingRecoverierDto;
    }

    public GlueMaterialBatchVerifyDataDTO setGluingRecoverierDto(StaffDTO gluingRecoverierDto) {
        this.gluingRecoverierDto = gluingRecoverierDto;
        return this;
    }

    public BigDecimal getRecycleNetWeight() {
        return recycleNetWeight;
    }

    public GlueMaterialBatchVerifyDataDTO setRecycleNetWeight(BigDecimal recycleNetWeight) {
        this.recycleNetWeight = recycleNetWeight;
        return this;
    }

    public StaffDTO getRecoverierDto() {
        return recoverierDto;
    }

    public GlueMaterialBatchVerifyDataDTO setRecoverierDto(StaffDTO recoverierDto) {
        this.recoverierDto = recoverierDto;
        return this;
    }

    public LocalDateTime getRecoveryTime() {
        return recoveryTime;
    }

    public GlueMaterialBatchVerifyDataDTO setRecoveryTime(LocalDateTime recoveryTime) {
        this.recoveryTime = recoveryTime;
        return this;
    }

    public LocalDateTime getTheoreticalRecoveryTime() {
        return theoreticalRecoveryTime;
    }

    public GlueMaterialBatchVerifyDataDTO setTheoreticalRecoveryTime(LocalDateTime theoreticalRecoveryTime) {
        this.theoreticalRecoveryTime = theoreticalRecoveryTime;
        return this;
    }

    public LocalDateTime getRewarmingTime() {
        return rewarmingTime;
    }

    public GlueMaterialBatchVerifyDataDTO setRewarmingTime(LocalDateTime rewarmingTime) {
        this.rewarmingTime = rewarmingTime;
        return this;
    }

    public LocalDateTime getStorageExpireTime() {
        return storageExpireTime;
    }

    public GlueMaterialBatchVerifyDataDTO setStorageExpireTime(LocalDateTime storageExpireTime) {
        this.storageExpireTime = storageExpireTime;
        return this;
    }

    public LocalDateTime getDeaerationTime() {
        return deaerationTime;
    }

    public GlueMaterialBatchVerifyDataDTO setDeaerationTime(LocalDateTime deaerationTime) {
        this.deaerationTime = deaerationTime;
        return this;
    }

    public LocalDateTime getQualityExpireTime() {
        return qualityExpireTime;
    }

    public GlueMaterialBatchVerifyDataDTO setQualityExpireTime(LocalDateTime qualityExpireTime) {
        this.qualityExpireTime = qualityExpireTime;
        return this;
    }

    public LocalDateTime getProductionTime() {
        return productionTime;
    }

    public GlueMaterialBatchVerifyDataDTO setProductionTime(LocalDateTime productionTime) {
        this.productionTime = productionTime;
        return this;
    }

    public Integer getMaxRewarmingTimes() {
        return maxRewarmingTimes;
    }

    public GlueMaterialBatchVerifyDataDTO setMaxRewarmingTimes(Integer maxRewarmingTimes) {
        this.maxRewarmingTimes = maxRewarmingTimes;
        return this;
    }

    public Integer getRewarmingTimes() {
        return rewarmingTimes;
    }

    public GlueMaterialBatchVerifyDataDTO setRewarmingTimes(Integer rewarmingTimes) {
        this.rewarmingTimes = rewarmingTimes;
        return this;
    }

    public String getNote() {
        return note;
    }

    public GlueMaterialBatchVerifyDataDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public GlueStepDTO getCurrentGlueStep() {
        return currentGlueStep;
    }

    public GlueMaterialBatchVerifyDataDTO setCurrentGlueStep(GlueStepDTO currentGlueStep) {
        this.currentGlueStep = currentGlueStep;
        return this;
    }

    public GlueStepDTO getAfterGlueStep() {
        return afterGlueStep;
    }

    public GlueMaterialBatchVerifyDataDTO setAfterGlueStep(GlueStepDTO afterGlueStep) {
        this.afterGlueStep = afterGlueStep;
        return this;
    }

    public GlueWorkFlowDTO getGlueWorkFlow() {
        return glueWorkFlow;
    }

    public GlueMaterialBatchVerifyDataDTO setGlueWorkFlow(GlueWorkFlowDTO glueWorkFlow) {
        this.glueWorkFlow = glueWorkFlow;
        return this;
    }
}
