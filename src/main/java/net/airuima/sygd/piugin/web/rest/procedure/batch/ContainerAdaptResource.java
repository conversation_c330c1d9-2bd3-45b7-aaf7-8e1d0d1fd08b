package net.airuima.sygd.piugin.web.rest.procedure.batch;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import com.alibaba.fastjson.JSON;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.dto.ExportParamDTO;
import net.airuima.sygd.piugin.service.procedure.batch.ContainerAdaptService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerCheckDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerCreateDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerResponseDTO;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.BaseResource;
import net.airuima.web.rest.procedure.batch.dto.ContainerDetailDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器Resource")
@RestController
@RequestMapping("/plugins/api/containers")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Container")
public class ContainerAdaptResource extends BaseResource<Container> {

    private final ContainerAdaptService containerAdaptService;

    public ContainerAdaptResource(ContainerAdaptService containerAdaptService) {
        this.containerAdaptService = containerAdaptService;
        this.mapUri = "/plugins/api/containers";
    }


    /**
     * 通过子工单创建容器
     *
     * @param containerCreateDTO 容器创建DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < ContainerResponseDTO>>> 容器列表
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过子工单创建容器")
    @PostMapping("/sub-worksheet/container")
    public ResponseEntity<ResponseData<List<ContainerResponseDTO>>> createContainerBySubWorksheet(@RequestBody ContainerCreateDTO containerCreateDTO) {
        try {
            List<ContainerResponseDTO> containerResponseDTOList = containerAdaptService.createContainerBySubWorksheet(containerCreateDTO);
            return ResponseData.ok(containerResponseDTOList);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 通过容器编码获取容器信息
     *
     * @param code 容器编码
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.lang.Void>> 容器信息
     */
    @Operation(summary = "根据容器编码获取容器信息")
    @GetMapping("/code/{code}")
    public ResponseEntity<ResponseData<ContainerDTO>> getContainerByCode(@PathVariable("code") String code) {
        try {
            return ResponseData.ok(containerAdaptService.getContainerByCode(code));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    /**
     * 子工单容器导入
     *
     * @param file 工子工单容器导入
     * @return org.springframework.http.ResponseEntity<java.lang.Void>通用返回对象
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "子工单容器导入")
    @PostMapping("/sub-worksheet/container/import")
    public ResponseEntity<Void> importTableExcel(@RequestParam("file") MultipartFile file, HttpServletResponse response) throws Exception {
        this.prepareImportParams();
        String data = "[{\"fieldName\":\"workSheet.serialNumber\",\"label\":\"工单号\"},{\"fieldName\":\"subWorkSheet.serialNumber\",\"label\":\"子工单号\"},{\"fieldName\":\"container.code\",\"label\":\"容器编码\"}]";
        List<ExportParamDTO> exportParamDTOList = JSON.parseArray(data, ExportParamDTO.class);
        List<Map<String, Object>> illegalDataList = containerAdaptService.importExcel(file);
        //获取excel文件信息
        List<Map<String, Object>> rowList = ExcelImportUtil.importExcel(file.getInputStream(), Map.class, this.importParams);
        // 返回不合法的数据
        if (!illegalDataList.isEmpty()) {
            int failedSize = illegalDataList.size();
            List<ExcelExportEntity> excelExportEntityList = exportParamDTOList.stream().map(s -> StringUtils.substringBefore(s.getLabel(), "[[")).map(label -> new ExcelExportEntity(label, label)).collect(Collectors.toList());
            excelExportEntityList.add(new ExcelExportEntity("错误信息", "错误信息"));
            String originalFilename = file.getOriginalFilename();
            if (null == originalFilename || originalFilename.isEmpty()) {
                return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(StringUtils.uncapitalize(String.class.getSimpleName()), "fileNameEmpty", "文件名为空")).build();
            }
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(null, "", originalFilename.contains("xlsx") ? ExcelType.XSSF : ExcelType.HSSF), excelExportEntityList, illegalDataList);
            response.setContentType(originalFilename.contains("xlsx") ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(originalFilename, "utf-8"));
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setHeader("X-app-alert", "app.import.failure");
            String errorMessage = "上传数据" + rowList.size() + "条,导入成功" + (rowList.size() - failedSize) + "条,导入失败" + failedSize + "条,请检查下载的文件,检查失败的详细原因";
            response.setHeader(HeaderUtil.APP_PARAMS, URLEncoder.encode(errorMessage, "UTF-8"));
            response.setHeader(HeaderUtil.APP_ERROR_MESSAGE, URLEncoder.encode(errorMessage, "UTF-8"));
            workbook.write(response.getOutputStream());
            return ResponseEntity.badRequest().headers(HeaderUtil.failureAlert("import")).build();
        } else {
            return ResponseEntity.ok().headers(HeaderUtil.succeedAlert("import")).build();
        }
    }

}
