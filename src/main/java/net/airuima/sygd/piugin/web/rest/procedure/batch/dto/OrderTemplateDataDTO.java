package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;
import java.util.Map;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 模板数据DTO
 *
 * <AUTHOR>
 * @date 2024-08-08
 */
@Schema(description = "模板数据DTO")
public class OrderTemplateDataDTO {

    /**
     * 标签通用模板数据
     */
    @Schema(description = "通用模板数据")
    private Map<String, List<String>> commonDataMap;

    /**
     * 表格主体标签数据
     */
    @Schema(description = "表格主体标签数据")
    private List<Map<String,Object>> rowDataList;

    public Map<String, List<String>> getCommonDataMap() {
        return commonDataMap;
    }

    public OrderTemplateDataDTO setCommonDataMap(Map<String, List<String>> commonDataMap) {
        this.commonDataMap = commonDataMap;
        return this;
    }

    public List<Map<String, Object>> getRowDataList() {
        return rowDataList;
    }

    public OrderTemplateDataDTO setRowDataList(List<Map<String, Object>> rowDataList) {
        this.rowDataList = rowDataList;
        return this;
    }
}
