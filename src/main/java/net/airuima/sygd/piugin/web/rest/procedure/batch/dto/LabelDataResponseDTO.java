package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标签数据返回DTO
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Schema(description = "标签数据返回DTO")
public class LabelDataResponseDTO {

    /**
     * 芯片批次编码
     */
    @Schema(description = "芯片批次编码")
    private String chipBatchCode;


    /**
     * 入库检操作员
     */
    @Schema(description = "入库检操作员")
    private String storeInCheckUserName;

    /**
     * 封帽机
     */
    @Schema(description = "封帽机")
    private String sealMachine;

    /**
     * 共晶机
     */
    @Schema(description = "共晶机")
    private String eutecticMachine;

    /**
     * 机器列表
     */
    @Schema(description = "机器列表")
    private List<MachineDTO> machineList;


    public List<MachineDTO> getMachineList() {
        return machineList;
    }

    public LabelDataResponseDTO setMachineList(List<MachineDTO> machineList) {
        this.machineList = machineList;
        return this;
    }

    public String getEutecticMachine() {
        return eutecticMachine;
    }

    public LabelDataResponseDTO setEutecticMachine(String eutecticMachine) {
        this.eutecticMachine = eutecticMachine;
        return this;
    }

    public String getSealMachine() {
        return sealMachine;
    }

    public LabelDataResponseDTO setSealMachine(String sealMachine) {
        this.sealMachine = sealMachine;
        return this;
    }

    public String getStoreInCheckUserName() {
        return storeInCheckUserName;
    }

    public LabelDataResponseDTO setStoreInCheckUserName(String storeInCheckUserName) {
        this.storeInCheckUserName = storeInCheckUserName;
        return this;
    }

    public String getChipBatchCode() {
        return chipBatchCode;
    }

    public LabelDataResponseDTO setChipBatchCode(String chipBatchCode) {
        this.chipBatchCode = chipBatchCode;
        return this;
    }


}
