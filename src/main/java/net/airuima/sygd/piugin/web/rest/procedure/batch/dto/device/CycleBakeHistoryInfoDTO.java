package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.constant.Constants;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "温循取出历史参数")
public class CycleBakeHistoryInfoDTO implements Serializable {

    /**
     * 箱号
     */
    @Schema(description = "箱号")
    private String caseNo;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    /**
     * SN
     */
    @Schema(description = "SN")
    private String sn;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private int number;

    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    /**
     * 出入温度范围
     */
    @Schema(description = "出入温度范围")
    private String temperatureRange;

    /**
     * 高温范围
     */
    @Schema(description = "高温范围")
    private String highTemperatureRange;

    /**
     * 低温范围
     */
    @Schema(description = "低温范围")
    private String lowTemperatureRange;

    /**
     * 温循次数
     */
    @Schema(description = "温循次数")
    private int times;

    /**
     * sn返修次数
     */
    @Schema(description = "sn返修次数")
    private Integer reworkTime = Constants.INT_ZERO;

    public Integer getReworkTime() {
        return reworkTime;
    }

    public CycleBakeHistoryInfoDTO setReworkTime(Integer reworkTime) {
        this.reworkTime = reworkTime;
        return this;
    }

    public String getCaseNo() {
        return caseNo;
    }

    public CycleBakeHistoryInfoDTO setCaseNo(String caseNo) {
        this.caseNo = caseNo;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public CycleBakeHistoryInfoDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSn() {
        return sn;
    }

    public CycleBakeHistoryInfoDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }

    public int getNumber() {
        return number;
    }

    public CycleBakeHistoryInfoDTO setNumber(int number) {
        this.number = number;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public CycleBakeHistoryInfoDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public CycleBakeHistoryInfoDTO setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }

    public String getHighTemperatureRange() {
        return highTemperatureRange;
    }

    public CycleBakeHistoryInfoDTO setHighTemperatureRange(String highTemperatureRange) {
        this.highTemperatureRange = highTemperatureRange;
        return this;
    }

    public String getLowTemperatureRange() {
        return lowTemperatureRange;
    }

    public CycleBakeHistoryInfoDTO setLowTemperatureRange(String lowTemperatureRange) {
        this.lowTemperatureRange = lowTemperatureRange;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public CycleBakeHistoryInfoDTO setTimes(int times) {
        this.times = times;
        return this;
    }
}
