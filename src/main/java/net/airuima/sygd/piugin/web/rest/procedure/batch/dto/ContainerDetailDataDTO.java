package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.dto.organization.StaffDTO;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器详情
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Schema(name = "容器详情")
public class ContainerDetailDataDTO extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作员工id
     */
    @Schema(description = "操作员工id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long staffId;


    private StaffDTO staffDto = new StaffDTO();

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;


    /**
     * 绑定状态
     */
    @Schema(description = "绑定状态(0:解绑;1:绑定)")
    private Integer status;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer inputNumber;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Integer qualifiedNumber;

    /**
     * 不合格数量
     */
    @Schema(description = "不合格数量")
    private Integer unqualifiedNumber;

    /**
     * 待流转数量
     */
    @Schema(description = "待流转数量")
    private Integer transferNumber;


    /**
     * 原始容器列表
     */
    @Schema(description = "原始容器列表")
    private String preContainerCodeList;

    /**
     * 转换容器列表
     */
    @Schema(description = "转换容器列表")
    private String afterContainerCodeList;


    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "容器生产状态：0:正常态,1:待维修")
    private Integer maintainStatus;


    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public StaffDTO getStaffDto() {
        return staffDto;
    }

    public void setStaffDto(StaffDTO staffDto) {
        this.staffDto = staffDto;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public void setContainerCode(String containerCode) {
        this.containerCode = containerCode;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getInputNumber() {
        return inputNumber;
    }

    public void setInputNumber(Integer inputNumber) {
        this.inputNumber = inputNumber;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public void setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public void setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
    }

    public Integer getTransferNumber() {
        return transferNumber;
    }

    public void setTransferNumber(Integer transferNumber) {
        this.transferNumber = transferNumber;
    }

    public String getPreContainerCodeList() {
        return preContainerCodeList;
    }

    public void setPreContainerCodeList(String preContainerCodeList) {
        this.preContainerCodeList = preContainerCodeList;
    }

    public String getAfterContainerCodeList() {
        return afterContainerCodeList;
    }

    public void setAfterContainerCodeList(String afterContainerCodeList) {
        this.afterContainerCodeList = afterContainerCodeList;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public Integer getMaintainStatus() {
        return maintainStatus;
    }

    public void setMaintainStatus(Integer maintainStatus) {
        this.maintainStatus = maintainStatus;
    }
}
