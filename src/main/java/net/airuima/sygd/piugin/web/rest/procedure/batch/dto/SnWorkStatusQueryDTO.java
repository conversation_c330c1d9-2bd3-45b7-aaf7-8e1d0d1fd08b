package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 查询sn状态
 *
 * <AUTHOR>
 */
@Schema(description = "查询sn状态")
public class SnWorkStatusQueryDTO {

    @Schema(description = "sn")
    private String sn;

    public String getSn() {
        return sn;
    }

    public SnWorkStatusQueryDTO setSn(String sn) {
        this.sn = sn;
        return this;
    }
}
