package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水批次请求
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Schema(description = "胶水批次请求DTO")
public class GlueBatchRequestDTO {

    @Schema(description = "编码")
    private String code;

    public String getCode() {
        return code;
    }

    public GlueBatchRequestDTO setCode(String code) {
        this.code = code;
        return this;
    }
}
