package net.airuima.sygd.piugin.web.rest.rworker;

import net.airuima.sygd.piugin.service.rworker.NextTodoWsStepService;
import net.airuima.sygd.piugin.web.rest.rworker.dto.NextTodoWsStepDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2023, 武汉睿码智能科技有限公司
 * 获取 工单/容器/sn 待做工序
 *
 * <AUTHOR>
 * @date 2023/1/31
 */
@RestController
@RequestMapping("/plugins/api/rworker")
public class NextTodoWsStepResource {

    @Autowired
    private NextTodoWsStepService nextTodoWsStepService;

    @PostMapping("/find-next-todo-ws-step")
    public ResponseEntity<ResponseData<Object>> findNextTodoWsStep(@RequestBody NextTodoWsStepDTO nextTodoWsStepDTO) {
        try {
            return ResponseData.ok(nextTodoWsStepService.findNextTodoWsStep(nextTodoWsStepDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
