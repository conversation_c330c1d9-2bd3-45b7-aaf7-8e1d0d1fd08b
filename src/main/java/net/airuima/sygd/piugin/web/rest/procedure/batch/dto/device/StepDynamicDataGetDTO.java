package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.document.DocumentDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 动态数据表单DTO
 *
 * <AUTHOR>
 * @date 2022/11/14 16:56
 */
@Schema(description = "动态数据表单DTO")
public class StepDynamicDataGetDTO implements Serializable {
    /**
     * 动态数据id
     */
    @Schema(description = "动态数据id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long dynamicDataId;

    /**
     * 动态数据名称
     */
    @Schema(description = "动态数据名称")
    private String dynamicDataName;

    /**
     * 动态数据编码
     */
    @Schema(description = "动态数据编码")
    private String dynamicDataCode;


    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;


    /**
     * 动态元数据信息集合
     */
    @Schema(description = "动态元数据信息集合")
    private List<ColumnInfo> columnInfoList;

    /**
     * 动态元数据信息
     */
    @Schema(description = "动态元数据信息")
    public static class ColumnInfo implements Serializable {
        /**
         * 动态元数据名称
         */
        @Schema(description = "动态元数据名称")
        private String columnName;

        /**
         * 动态元数据编码
         */
        @Schema(description = "动态元数据编码")
        private String columnCode;

        /**
         * 动态元数据值
         */
        @Schema(description = "动态元数据值")
        private String columnValue;

        /**
         * 引导信息
         */
        @Schema(description = "引导信息")
        private String guidance;

        /**
         * 提示信息
         */
        @Schema(description = "提示信息")
        private String prompt;

        /**
         * 表格展示顺序
         */
        @Schema(description = "表格展示顺序")
        private Integer tableOrder;

        /**
         * 表单展示顺序
         */
        @Schema(description = "表单展示顺序")
        private Integer formOrder;

        /**
         * 字段类型
         */
        @Schema(description = "字段类型")
        private String category;

        /**
         * 前端组件
         */
        @Schema(description = "前端组件")
        private String widget;

        /**
         * 前端组件数据
         */
        @Schema(description = "前端组件数据")
        private String widgetData;

        /**
         * 前端字段验证
         */
        @Schema(description = "前端字段验证")
        private String columnValidate;

        /**
         * 是否启用
         */
        @Schema(description = "是否启用")
        private Boolean isEnable;


        /**
         * 判定结果(true:合格;false)
         *  数值类型的若在区间范围内则合格，反之不合格
         *  对于字符串类型、文件类型一律认为合格
         *  对于布尔值类型的则取用户的选项
         */
        @Schema(description = "判定结果(true:合格;false)(数值类型的若在区间范围内则合格，反之不合格,字符串、文件一律合格)")
        private boolean result = Boolean.TRUE;


        /**
         * 文件集合
         */
        @Schema(description = "文件集合")
        private List<DocumentDTO> documentDTOList;

        /**
         * 子动态元数据集合
         */
        @Schema(description = "子动态元数据集合")
        private List<SubDynamicDataColumnGetDTO> subDynamicDataColumnGetDTOList;

        public String getColumnValue() {
            return columnValue;
        }

        public ColumnInfo setColumnValue(String columnValue) {
            this.columnValue = columnValue;
            return this;
        }

        public boolean getResult() {
            return result;
        }

        public ColumnInfo setResult(boolean result) {
            this.result = result;
            return this;
        }

        public List<DocumentDTO> getDocumentDTOList() {
            return documentDTOList;
        }

        public ColumnInfo setDocumentDTOList(List<DocumentDTO> documentDTOList) {
            this.documentDTOList = documentDTOList;
            return this;
        }

        public Boolean getIsEnable() {
            return isEnable;
        }

        public ColumnInfo setIsEnable(Boolean isEnable) {
            this.isEnable = isEnable;
            return this;
        }

        public String getColumnName() {
            return columnName;
        }

        public ColumnInfo setColumnName(String columnName) {
            this.columnName = columnName;
            return this;
        }

        public String getColumnCode() {
            return columnCode;
        }

        public ColumnInfo setColumnCode(String columnCode) {
            this.columnCode = columnCode;
            return this;
        }

        public String getGuidance() {
            return guidance;
        }

        public ColumnInfo setGuidance(String guidance) {
            this.guidance = guidance;
            return this;
        }

        public String getPrompt() {
            return prompt;
        }

        public ColumnInfo setPrompt(String prompt) {
            this.prompt = prompt;
            return this;
        }

        public Integer getTableOrder() {
            return tableOrder;
        }

        public ColumnInfo setTableOrder(Integer tableOrder) {
            this.tableOrder = tableOrder;
            return this;
        }

        public Integer getFormOrder() {
            return formOrder;
        }

        public ColumnInfo setFormOrder(Integer formOrder) {
            this.formOrder = formOrder;
            return this;
        }

        public String getCategory() {
            return category;
        }

        public ColumnInfo setCategory(String category) {
            this.category = category;
            return this;
        }

        public String getWidget() {
            return widget;
        }

        public ColumnInfo setWidget(String widget) {
            this.widget = widget;
            return this;
        }

        public String getWidgetData() {
            return widgetData;
        }

        public ColumnInfo setWidgetData(String widgetData) {
            this.widgetData = widgetData;
            return this;
        }

        public String getColumnValidate() {
            return columnValidate;
        }

        public ColumnInfo setColumnValidate(String columnValidate) {
            this.columnValidate = columnValidate;
            return this;
        }

        public List<SubDynamicDataColumnGetDTO> getSubStepDynamicDataColumnGetDTOList() {
            return subDynamicDataColumnGetDTOList;
        }

        public ColumnInfo setSubStepDynamicDataColumnGetDTOList(List<SubDynamicDataColumnGetDTO> subDynamicDataColumnGetDTOList) {
            this.subDynamicDataColumnGetDTOList = subDynamicDataColumnGetDTOList;
            return this;
        }
    }

    public String getStepName() {
        return stepName;
    }

    public StepDynamicDataGetDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public StepDynamicDataGetDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public Long getDynamicDataId() {
        return dynamicDataId;
    }

    public StepDynamicDataGetDTO setDynamicDataId(Long dynamicDataId) {
        this.dynamicDataId = dynamicDataId;
        return this;
    }

    public String getDynamicDataName() {
        return dynamicDataName;
    }

    public StepDynamicDataGetDTO setDynamicDataName(String dynamicDataName) {
        this.dynamicDataName = dynamicDataName;
        return this;
    }

    public String getDynamicDataCode() {
        return dynamicDataCode;
    }

    public StepDynamicDataGetDTO setDynamicDataCode(String dynamicDataCode) {
        this.dynamicDataCode = dynamicDataCode;
        return this;
    }

    public List<ColumnInfo> getColumnInfoList() {
        return columnInfoList;
    }

    public StepDynamicDataGetDTO setColumnInfoList(List<ColumnInfo> columnInfoList) {
        this.columnInfoList = columnInfoList;
        return this;
    }
}
