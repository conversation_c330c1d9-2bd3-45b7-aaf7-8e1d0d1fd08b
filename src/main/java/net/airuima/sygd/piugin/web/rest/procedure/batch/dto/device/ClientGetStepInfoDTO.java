package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.domain.base.calibrate.CalibrateRule;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.process.WorkFlowStep;
import net.airuima.domain.base.quality.UnqualifiedItem;
import net.airuima.domain.base.wearingpart.WearingPartExchange;
import net.airuima.domain.procedure.batch.WsStep;
import net.airuima.domain.procedure.single.SnWorkStatus;
import net.airuima.dto.client.base.BaseClientDTO;
import net.airuima.dto.document.DocumentDTO;
import net.airuima.dto.dynamic.StepDynamicDataGetDTO;
import net.airuima.dto.pedigree.SnRuleDTO;
import net.airuima.dto.process.StepDTO;
import net.airuima.dto.rfms.FacilityDTO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWorker请求工序相关接口DTO
 *
 * <AUTHOR>
 * @date 2020/12/29
 */
@Schema(description = "Rwork请求待做工序返回信息")
public class ClientGetStepInfoDTO extends BaseClientDTO {
    /**
     * 动态数据DTO集合
     */
    @Schema(description = "动态数据DTO集合")
    private StepDynamicDataGetDTO stepDynamicDataGetDTO;

    /**
     * 工艺路线
     */
    @Schema(description = "工艺路线")
    private WorkFlowInfo workFlowInfo;

    /**
     * 产品谱系名称
     */
    @Schema(description = "产品谱系名称")
    private String pedigreeName;

    /**
     * 产品谱系编码
     */
    @Schema(description = "产品谱系编码")
    private String pedigreeCode;

    /**
     * 产品谱系型号
     */
    @Schema(description = "产品谱系规格型号")
    private String pedigreeSpecification;

    /**
     * 工序id
     */
    @Schema(description = "工序ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepId;

    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;

    /**
     * 工序类型
     */
    @Schema(description = "工序类型")
    private Integer stepCategory;

    /**
     * 当前工序投产数
     */
    @Schema(description = "工序投产数")
    private Integer number;

    /**
     * 当前工序实际完成数量
     */
    @Schema(description = "当前工序实际完成数量")
    private Integer finishNumber;

    /**
     *  当前工序实际还需待做数量
     */
    @Schema(description = "当前工序实际还需待做数量")
    private Integer pendingNumber;
    /**
     * 工序实际待完成数量
     */
    @Schema(description = "工序实际待完成数量(抽检工序数量可能会和投产数不相等)")
    private Integer realInputnumber;

    /**
     * 最大不合格数
     */
    @Schema(description = "最大不合格数")
    private Integer maxUnQualifiedNumber;

    /**
     * 子工单id
     */
    @Schema(description = "子工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subWsId;

    /**
     * 子工单分单信息备注，供RWorker进行展示
     */
    @Schema(description = "子工单分单信息备注，供RWorker进行展示")
    private String note;

    /**
     * 总工单id
     */
    @Schema(description = "总工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long wsId;

    /**
     * 工位id
     */
    @Schema(description = "工位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    private String clientName;

    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    private String clientCode;

    /**
     * 管控模式(0:批量;1:单支)
     */
    @Schema(description = "管控模式")
    private Integer controlMode;

    /**
     * 请求模式(0:工单请求;1:容器请求;2:单支请求)
     */
    private Integer requestMode;

    /**
     * 是否绑定容器(0:否;1:是)
     */
    @Schema(description = "是否绑定容器")
    private Boolean isBindContainer;

    /**
     * 是否管控物料
     */
    @Schema(description = "是否管控物料")
    private Boolean isControlMaterial;

    /**
     * 不良项目集合
     */
    @Schema(description = "不良项目集合")
    private List<UnqualifiedItemInfo> unqualifiedItemInfoList;

    /**
     * 定制工序数组
     */
    @Schema(description = "工单定制工序列表")
    private List<WsStepInfo> wsStepInfoList;

    /**
     * 上料集合
     */
    @Schema(description = "工序上料集合")
    private List<MaterialRuleInfo> materialRuleInfoList;

    /**
     * 工位工序绑定的设备信息
     */
    @Schema(description = "工位工序绑定的设备信息")
    private List<EquipmentInfo> equipmentInfoList;

    /**
     * SN数组
     */
    @Schema(description = "SN集合")
    private List<SnInfo> snInfoList;

    /**
     * fqc信息
     */
    @Schema(description = "FQC信息")
    private FqcInfo fqcInfo;

    /**
     * 抽检信息
     */
    @Schema(description = "抽检信息")
    private RandomInspectGetInfo randomInspectGetInfo;

    @Schema(description = "工序SOP文件")
    private List<DocumentDTO> documentDTOList;

    @Schema(description = "工序技术指标")
    private String specification;

    /**
     * 易损件规则信息
     */
    @Schema(description = "易损件规则信息")
    private List<PedigreeStepWearingPartGroupInfo> pedigreeStepWearingPartGroupInfoList;

    /**
     * 防重复提交Token(采用32位uuid)
     */
    @Schema(description = "防重复提交Token")
    private String xsrfToken;

    /**
     * 工序与工序之间的下交间隔是否超出时间间隔(0,没有;1,超时)
     */
    @Schema(description = "工序与工序之间的下交间隔是否超出时长间隔(0,没有;1,超时)")
    private boolean isTimeOut;

    /**
     * 当前工序时间间隔
     */
    @Schema(description = "当前工序时间间隔")
    private String currStepTimeInterval;

    /**
     * 工位GRR过期日期
     */
    @Schema(description = "工位GRR到期日期")
    private LocalDateTime grrExpireTime;

    /**
     * 工位GRR到期提前提醒天数
     */
    @Schema(description = "工位GRR到期提前提醒天数")
    private Long beforeGrrTipDay;

    /**
     * 设备校准提醒信息列表
     */
    @Schema(description = "设备校准提醒信息列表")
    private List<FacilityCalibrateWaringInfo> facilityCalibrateWaringInfoList;

    /**
     * 烘烤放入工序参数
     */
    @Schema(description = "烘烤放入工序参数")
    private BakeConfigInfoDTO bakeConfigInfo;
    /**
     * 温循放入工序参数
     */
    @Schema(description = "温循放入工序参数")
    private CycleBakeConfigInfoDTO cycleBakeConfigInfo;
    /**
     * 老化放入工序参数
     */
    @Schema(description = "老化放入工序参数")
    private AgeingConfigInfoDTO ageingConfigInfo;
    /**
     * 烘烤温循老化历史放入时间参数
     */
    @Schema(description = "烘烤温循老化历史放入时间参数")
    private BakeCycleBakeAgeingHistoryPutInDateDTO bakeCycleBakeAgeingHistoryPutInDateDTO;

    public ClientGetStepInfoDTO() {
    }

    public ClientGetStepInfoDTO(BaseClientDTO baseClientDTO) {
        this.setStatus(baseClientDTO.getStatus());
        this.setMessage(baseClientDTO.getMessage());
    }

    public WorkFlowInfo getWorkFlowInfo() {
        return workFlowInfo;
    }

    public ClientGetStepInfoDTO setWorkFlowInfo(WorkFlowInfo workFlowInfo) {
        this.workFlowInfo = workFlowInfo;
        return this;
    }

    public BakeConfigInfoDTO getBakeConfigInfo() {
        return bakeConfigInfo;
    }

    public ClientGetStepInfoDTO setBakeConfigInfo(BakeConfigInfoDTO bakeConfigInfo) {
        this.bakeConfigInfo = bakeConfigInfo;
        return this;
    }

    public CycleBakeConfigInfoDTO getCycleBakeConfigInfo() {
        return cycleBakeConfigInfo;
    }

    public ClientGetStepInfoDTO setCycleBakeConfigInfo(CycleBakeConfigInfoDTO cycleBakeConfigInfo) {
        this.cycleBakeConfigInfo = cycleBakeConfigInfo;
        return this;
    }

    public AgeingConfigInfoDTO getAgeingConfigInfo() {
        return ageingConfigInfo;
    }

    public ClientGetStepInfoDTO setAgeingConfigInfo(AgeingConfigInfoDTO ageingConfigInfo) {
        this.ageingConfigInfo = ageingConfigInfo;
        return this;
    }

    public BakeCycleBakeAgeingHistoryPutInDateDTO getBakeCycleBakeAgeingHistoryPutInDateDTO() {
        return bakeCycleBakeAgeingHistoryPutInDateDTO;
    }

    public ClientGetStepInfoDTO setBakeCycleBakeAgeingHistoryPutInDateDTO(BakeCycleBakeAgeingHistoryPutInDateDTO bakeCycleBakeAgeingHistoryPutInDateDTO) {
        this.bakeCycleBakeAgeingHistoryPutInDateDTO = bakeCycleBakeAgeingHistoryPutInDateDTO;
        return this;
    }

    public Integer getFinishNumber() {
        return finishNumber;
    }

    public ClientGetStepInfoDTO setFinishNumber(Integer finishNumber) {
        this.finishNumber = finishNumber;
        return this;
    }

    public Integer getPendingNumber() {
        return pendingNumber;
    }

    public ClientGetStepInfoDTO setPendingNumber(Integer pendingNumber) {
        this.pendingNumber = pendingNumber;
        return this;
    }

    public String getCurrStepTimeInterval() {
        return currStepTimeInterval;
    }

    public ClientGetStepInfoDTO setCurrStepTimeInterval(String currStepTimeInterval) {
        this.currStepTimeInterval = currStepTimeInterval;
        return this;
    }

    public boolean isTimeOut() {
        return isTimeOut;
    }

    public ClientGetStepInfoDTO setTimeOut(boolean timeOut) {
        isTimeOut = timeOut;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public ClientGetStepInfoDTO setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getPedigreeCode() {
        return pedigreeCode;
    }

    public ClientGetStepInfoDTO setPedigreeCode(String pedigreeCode) {
        this.pedigreeCode = pedigreeCode;
        return this;
    }

    public String getPedigreeSpecification() {
        return pedigreeSpecification;
    }

    public ClientGetStepInfoDTO setPedigreeSpecification(String pedigreeSpecification) {
        this.pedigreeSpecification = pedigreeSpecification;
        return this;
    }

    public Long getStepId() {
        return stepId;
    }

    public ClientGetStepInfoDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public ClientGetStepInfoDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public ClientGetStepInfoDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public Integer getStepCategory() {
        return stepCategory;
    }

    public ClientGetStepInfoDTO setStepCategory(Integer stepCategory) {
        this.stepCategory = stepCategory;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ClientGetStepInfoDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Long getSubWsId() {
        return subWsId;
    }

    public ClientGetStepInfoDTO setSubWsId(Long subWsId) {
        this.subWsId = subWsId;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public void setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
    }

    public String getSpecification() {
        return specification;
    }

    public ClientGetStepInfoDTO setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ClientGetStepInfoDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Long getWsId() {
        return wsId;
    }

    public ClientGetStepInfoDTO setWsId(Long wsId) {
        this.wsId = wsId;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ClientGetStepInfoDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public StepDynamicDataGetDTO getStepDynamicDataGetDTO() {
        return stepDynamicDataGetDTO;
    }

    public ClientGetStepInfoDTO setStepDynamicDataGetDTO(StepDynamicDataGetDTO stepDynamicDataGetDTO) {
        this.stepDynamicDataGetDTO = stepDynamicDataGetDTO;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public ClientGetStepInfoDTO setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public ClientGetStepInfoDTO setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public Integer getControlMode() {
        return controlMode;
    }

    public ClientGetStepInfoDTO setControlMode(Integer controlMode) {
        this.controlMode = controlMode;
        return this;
    }

    public Boolean getIsBindContainer() {
        return isBindContainer;
    }

    public ClientGetStepInfoDTO setIsBindContainer(Boolean isBindContainer) {
        this.isBindContainer = isBindContainer;
        return this;
    }

    public Integer getRequestMode() {
        return requestMode;
    }

    public ClientGetStepInfoDTO setRequestMode(Integer requestMode) {
        this.requestMode = requestMode;
        return this;
    }

    public List<UnqualifiedItemInfo> getUnqualifiedItemInfoList() {
        return unqualifiedItemInfoList;
    }

    public ClientGetStepInfoDTO setUnqualifiedItemInfoList(List<UnqualifiedItemInfo> unqualifiedItemInfoList) {
        this.unqualifiedItemInfoList = unqualifiedItemInfoList;
        return this;
    }

    public List<WsStepInfo> getWsStepInfoList() {
        return wsStepInfoList;
    }

    public ClientGetStepInfoDTO setWsStepInfoList(List<WsStepInfo> wsStepInfoList) {
        this.wsStepInfoList = wsStepInfoList;
        return this;
    }

    public List<SnInfo> getSnInfoList() {
        return snInfoList;
    }

    public ClientGetStepInfoDTO setSnInfoList(List<SnInfo> snInfoList) {
        this.snInfoList = snInfoList;
        return this;
    }

    public List<MaterialRuleInfo> getMaterialInfoList() {
        return materialRuleInfoList;
    }

    public ClientGetStepInfoDTO setMaterialInfoList(List<MaterialRuleInfo> materialRuleInfoList) {
        this.materialRuleInfoList = materialRuleInfoList;
        return this;
    }

    public List<EquipmentInfo> getEquipmentInfoList() {
        return equipmentInfoList;
    }

    public ClientGetStepInfoDTO setEquipmentInfoList(List<EquipmentInfo> equipmentInfoList) {
        this.equipmentInfoList = equipmentInfoList;
        return this;
    }

    public Boolean getIsControlMaterial() {
        return isControlMaterial;
    }

    public ClientGetStepInfoDTO setIsControlMaterial(Boolean isControlMaterial) {
        this.isControlMaterial = isControlMaterial;
        return this;
    }

    public FqcInfo getFqcInfo() {
        return fqcInfo;
    }

    public ClientGetStepInfoDTO setFqcInfo(FqcInfo fqcInfo) {
        this.fqcInfo = fqcInfo;
        return this;
    }

    public RandomInspectGetInfo getRandomInspectGetInfo() {
        return randomInspectGetInfo;
    }

    public ClientGetStepInfoDTO setRandomInspectGetInfo(RandomInspectGetInfo randomInspectGetInfo) {
        this.randomInspectGetInfo = randomInspectGetInfo;
        return this;
    }

    public Integer getMaxUnQualifiedNumber() {
        return maxUnQualifiedNumber;
    }

    public ClientGetStepInfoDTO setMaxUnQualifiedNumber(Integer maxUnQualifiedNumber) {
        this.maxUnQualifiedNumber = maxUnQualifiedNumber;
        return this;
    }

    public List<PedigreeStepWearingPartGroupInfo> getPedigreeStepWearingPartGroupInfoList() {
        return pedigreeStepWearingPartGroupInfoList;
    }

    public ClientGetStepInfoDTO setPedigreeStepWearingPartGroupInfoList(List<PedigreeStepWearingPartGroupInfo> pedigreeStepWearingPartGroupInfoList) {
        this.pedigreeStepWearingPartGroupInfoList = pedigreeStepWearingPartGroupInfoList;
        return this;
    }

    public String getXsrfToken() {
        return xsrfToken;
    }

    public ClientGetStepInfoDTO setXsrfToken(String xsrfToken) {
        this.xsrfToken = xsrfToken;
        return this;
    }
    public LocalDateTime getGrrExpireTime() {
        return grrExpireTime;
    }

    public ClientGetStepInfoDTO setGrrExpireTime(LocalDateTime grrExpireTime) {
        this.grrExpireTime = grrExpireTime;
        return this;
    }

    public Long getBeforeGrrTipDay() {
        return beforeGrrTipDay;
    }

    public ClientGetStepInfoDTO setBeforeGrrTipDay(Long beforeGrrTipDay) {
        this.beforeGrrTipDay = beforeGrrTipDay;
        return this;
    }

    public Integer getRealInputnumber() {
        return realInputnumber;
    }

    public ClientGetStepInfoDTO setRealInputnumber(Integer realInputnumber) {
        this.realInputnumber = realInputnumber;
        return this;
    }

    public List<FacilityCalibrateWaringInfo> getFacilityCalibrateWaringInfoList() {
        return facilityCalibrateWaringInfoList;
    }

    public ClientGetStepInfoDTO setFacilityCalibrateWaringInfoList(List<FacilityCalibrateWaringInfo> facilityCalibrateWaringInfoList) {
        this.facilityCalibrateWaringInfoList = facilityCalibrateWaringInfoList;
        return this;
    }

    /**
     * 请求工序传过来的参数
     */
    public static class StepInfo {

        /**
         * 子工单id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long subWorkSheetId;

        @Schema(description = "子工单编码")
        private String subWorkSheetCode;

        /**
         * 工位id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long workCellId;

        @Schema(description = "工位编码")
        private String workCellCode;

        @Schema(description = "工位IP")
        private String workCellIp;

        /**
         * 员工id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long staffId;

        @Schema(description = "员工编码")
        private String staffCode;

        /**
         * 请求的容器id列表
         */
        private List<Long>requestContainerIds;

        /**
         * 请求的容器编码列表
         */
        @Schema(description = "请求的容器编码列表")
        private List<String> requestContainerCodes;

        /**
         * 绑定的容器id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long bindContainerId;

        /**
         * 绑定的容器编码
         */
        @Schema(description = "绑定的容器编码")
        private String bindContainerCode;

        public String getSubWorkSheetCode() {
            return subWorkSheetCode;
        }

        public StepInfo setSubWorkSheetCode(String subWorkSheetCode) {
            this.subWorkSheetCode = subWorkSheetCode;
            return this;
        }

        public String getWorkCellCode() {
            return workCellCode;
        }

        public StepInfo setWorkCellCode(String workCellCode) {
            this.workCellCode = workCellCode;
            return this;
        }

        public String getStaffCode() {
            return staffCode;
        }

        public StepInfo setStaffCode(String staffCode) {
            this.staffCode = staffCode;
            return this;
        }

        public List<String> getRequestContainerCodes() {
            return requestContainerCodes;
        }

        public StepInfo setRequestContainerCodes(List<String> requestContainerCodes) {
            this.requestContainerCodes = requestContainerCodes;
            return this;
        }

        public String getBindContainerCode() {
            return bindContainerCode;
        }

        public StepInfo setBindContainerCode(String bindContainerCode) {
            this.bindContainerCode = bindContainerCode;
            return this;
        }

        public Long getSubWorkSheetId() {
            return subWorkSheetId;
        }

        public StepInfo setSubWorkSheetId(Long subWorkSheetId) {
            this.subWorkSheetId = subWorkSheetId;
            return this;
        }

        public Long getWorkCellId() {
            return workCellId;
        }

        public StepInfo setWorkCellId(Long workCellId) {
            this.workCellId = workCellId;
            return this;
        }

        public Long getStaffId() {
            return staffId;
        }

        public StepInfo setStaffId(Long staffId) {
            this.staffId = staffId;
            return this;
        }

        public List<Long> getRequestContainerIds() {
            return requestContainerIds;
        }

        public StepInfo setRequestContainerIds(List<Long> requestContainerIds) {
            this.requestContainerIds = requestContainerIds;
            return this;
        }

        public Long getBindContainerId() {
            return bindContainerId;
        }

        public StepInfo setBindContainerId(Long bindContainerId) {
            this.bindContainerId = bindContainerId;
            return this;
        }

        public String getWorkCellIp() {
            return workCellIp;
        }

        public StepInfo setWorkCellIp(String workCellIp) {
            this.workCellIp = workCellIp;
            return this;
        }
    }

    /**
     * 待做工序信息
     */
    public static class ToDoStepInfo {

        /**
         * 待做工序数组
         */
        private Step step;

        /**
         * 投产数
         */
        private Integer number;

        /**
         * 是否第一个工序
         */
        private Boolean isFirstStep;

        /**
         * 是否最后一个工序
         */
        private Boolean isLastStep;

        public Step getStep() {
            return step;
        }

        public ToDoStepInfo setStep(Step step) {
            this.step = step;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public ToDoStepInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public Boolean getFirstStep() {
            return isFirstStep;
        }

        public ToDoStepInfo setFirstStep(Boolean firstStep) {
            isFirstStep = firstStep;
            return this;
        }

        public Boolean getLastStep() {
            return isLastStep;
        }

        public ToDoStepInfo setLastStep(Boolean lastStep) {
            isLastStep = lastStep;
            return this;
        }
    }

    /**
     * SN信息
     */
    public static class SnInfo {

        /**
         * SN
         */
        private String sn;

        /**
         * SN状态
         */
        private Integer status;

        public SnInfo() {
        }

        public SnInfo(SnWorkStatus snWorkStatus) {
            this.sn = snWorkStatus.getSn();
            this.status = snWorkStatus.getStatus();
        }

        public String getSn() {
            return sn;
        }

        public SnInfo setSn(String sn) {
            this.sn = sn;
            return this;
        }

        public Integer getStatus() {
            return status;
        }

        public SnInfo setStatus(Integer status) {
            this.status = status;
            return this;
        }
    }

    /**
     * 不良项目
     */
    public static class UnqualifiedItemInfo {

        /**
         * 不合格项目id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 不合格项目类型（预留）
         */
        @Schema(description = "不合格项目类型（预留）")
        private Integer category;

        /**
         * 不合格项目代码
         */
        private String code;

        /**
         * 不合格项目名称
         */
        private String name;

        /**
         * 不良种类名称
         */
        private String unqualifiedGroupName;

        /**
         * 不良种类编码
         */
        private String unqualifiedGroupCode;

        @Schema(description = "最大不合格数")
        private Integer maxUnQualifiedNumber;

        public UnqualifiedItemInfo() {
        }

        public UnqualifiedItemInfo(UnqualifiedItem unqualifiedItem) {
            this.id = unqualifiedItem.getId();
            this.code = unqualifiedItem.getCode();
            this.name = unqualifiedItem.getName();
            if (Objects.nonNull(unqualifiedItem.getUnqualifiedGroup())){
                this.unqualifiedGroupName = unqualifiedItem.getUnqualifiedGroup().getName();
                this.unqualifiedGroupCode = unqualifiedItem.getUnqualifiedGroup().getCode();
            }
        }

        public Long getId() {
            return id;
        }

        public UnqualifiedItemInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getCode() {
            return code;
        }

        public UnqualifiedItemInfo setCode(String code) {
            this.code = code;
            return this;
        }

        public String getName() {
            return name;
        }

        public UnqualifiedItemInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getUnqualifiedGroupName() {
            return unqualifiedGroupName;
        }

        public UnqualifiedItemInfo setUnqualifiedGroupName(String unqualifiedGroupName) {
            this.unqualifiedGroupName = unqualifiedGroupName;
            return this;
        }

        public String getUnqualifiedGroupCode() {
            return unqualifiedGroupCode;
        }

        public UnqualifiedItemInfo setUnqualifiedGroupCode(String unqualifiedGroupCode) {
            this.unqualifiedGroupCode = unqualifiedGroupCode;
            return this;
        }

        public Integer getCategory() {
            return category;
        }

        public UnqualifiedItemInfo setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Integer getMaxUnQualifiedNumber() {
            return maxUnQualifiedNumber;
        }

        public UnqualifiedItemInfo setMaxUnQualifiedNumber(Integer maxUnQualifiedNumber) {
            this.maxUnQualifiedNumber = maxUnQualifiedNumber;
            return this;
        }
    }

    /**
     * 设备信息
     *
     * <AUTHOR>
     * @date 2021-01-18
     **/
    public static class EquipmentInfo {
        /**
         * 设备ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long equipmentId;
        /**
         * 设备编码
         */
        private String equipmentCode;
        /**
         * 设备名称
         */
        private String equipmentName;

        public EquipmentInfo() {

        }

        public EquipmentInfo(FacilityDTO facilityDto) {
            this.equipmentId = facilityDto.getId();
            this.equipmentCode = facilityDto.getCode();
            this.equipmentName = facilityDto.getName();
        }

        public Long getEquipmentId() {
            return equipmentId;
        }

        public EquipmentInfo setEquipmentId(Long equipmentId) {
            this.equipmentId = equipmentId;
            return this;
        }

        public String getEquipmentCode() {
            return equipmentCode;
        }

        public EquipmentInfo setEquipmentCode(String equipmentCode) {
            this.equipmentCode = equipmentCode;
            return this;
        }

        public String getEquipmentName() {
            return equipmentName;
        }

        public EquipmentInfo setEquipmentName(String equipmentName) {
            this.equipmentName = equipmentName;
            return this;
        }
    }

    /**
     * 工单定制工序信息
     */
    public static class WsStepInfo {

        /**
         * 前置工序列表，分号隔开
         */
        private String preStepId;

        /**
         * 后置工序列表，分号隔开
         */
        private String afterStepId;

        /**
         * 工序id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long stepId;

        /**
         * 工序名称
         */
        private String stepName;

        /**
         * 工序编码
         */
        private String stepCode;

        /**
         * 请求模式
         */
        @Schema(description = "请求模式")
        private Integer requestMode;

        /**
         * 管控模式(0:批量;1:单支)
         */
        @Schema(description = "管控模式(0:批量;1:单支)")
        private Integer controlMode;

        /**
         * 是否管控物料(0:不管控;1:管控)
         */
        @Schema(description = "是否管控物料(0:不管控;1:管控)")
        private boolean isControlMaterial;

        /**
         * 是否绑定容器(0:否;1:是)
         */
        @Schema(description = "是否绑定容器(0:否;1:是)")
        private boolean isBindContainer;

        public WsStepInfo() {
        }

        public WsStepInfo(WsStep wsStep) {
            this.preStepId = wsStep.getPreStepId();
            this.afterStepId = wsStep.getAfterStepId();
            this.stepId = wsStep.getStep().getId();
            this.stepName = wsStep.getStep().getName();
            this.stepCode = wsStep.getStep().getCode();
            this.controlMode = wsStep.getControlMode();
            this.requestMode = wsStep.getRequestMode();
            this.isBindContainer = wsStep.getIsBindContainer();
            this.isControlMaterial = wsStep.getIsControlMaterial();
        }

        public WsStepInfo(StepDTO stepDto) {
            this.preStepId = stepDto.getPreStepId();
            this.afterStepId = stepDto.getAfterStepId();
            this.stepId = stepDto.getId();
            this.stepName = stepDto.getName();
            this.stepCode = stepDto.getCode();
            this.controlMode = stepDto.getControlMode();
            this.requestMode = stepDto.getRequestMode();
            this.isBindContainer = stepDto.getIsBindContainer();
            this.isControlMaterial = stepDto.getIsControlMaterial();
        }

        public WsStepInfo(WorkFlowStep workFlowStep){
            this.preStepId = workFlowStep.getPreStepId();
            this.afterStepId = workFlowStep.getAfterStepId();
            this.stepId = workFlowStep.getStep().getId();
            this.stepName = workFlowStep.getStep().getName();
            this.stepCode = workFlowStep.getStep().getCode();
        }

        public String getPreStepId() {
            return preStepId;
        }

        public WsStepInfo setPreStepId(String preStepId) {
            this.preStepId = preStepId;
            return this;
        }

        public String getAfterStepId() {
            return afterStepId;
        }

        public WsStepInfo setAfterStepId(String afterStepId) {
            this.afterStepId = afterStepId;
            return this;
        }

        public Long getStepId() {
            return stepId;
        }

        public WsStepInfo setStepId(Long stepId) {
            this.stepId = stepId;
            return this;
        }

        public String getStepName() {
            return stepName;
        }

        public WsStepInfo setStepName(String stepName) {
            this.stepName = stepName;
            return this;
        }

        public String getStepCode() {
            return stepCode;
        }

        public WsStepInfo setStepCode(String stepCode) {
            this.stepCode = stepCode;
            return this;
        }

        public Integer getRequestMode() {
            return requestMode;
        }

        public WsStepInfo setRequestMode(Integer requestMode) {
            this.requestMode = requestMode;
            return this;
        }

        public Integer getControlMode() {
            return controlMode;
        }

        public WsStepInfo setControlMode(Integer controlMode) {
            this.controlMode = controlMode;
            return this;
        }

        public boolean isControlMaterial() {
            return isControlMaterial;
        }

        public WsStepInfo setControlMaterial(boolean controlMaterial) {
            isControlMaterial = controlMaterial;
            return this;
        }

        public boolean isBindContainer() {
            return isBindContainer;
        }

        public WsStepInfo setBindContainer(boolean bindContainer) {
            isBindContainer = bindContainer;
            return this;
        }
    }

    /**
     * 当前工序的上料规则
     */
    public static class MaterialRuleInfo {
        /**
         * 上料比例
         */
        private Double proportion;


        /**
         * 上料数量
         */
        private Double number;

        /**
         * 剩余数量
         */
        private Double leftNumber;

        /**
         * 当前物料id
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long materialId;

        /**
         * 当前物料名称
         */
        private String materialName;

        /**
         * 当前物料编码
         */
        private String materialCode;

        /**
         * 是否扣数(1:扣数;0:不扣数)
         */
        private Boolean isDeduct;

        /**
         * 是否核物料(1:核对物料编码;0:不核对物料编码)
         */
        private Boolean isCheckMaterial;

        /**
         * 是否核物料批次(1:核对物料批次;0:不核对物料批次)
         */
        private Boolean isCheckMaterialBatch;

        /**
         * 物料管控粒度(0:单只序列号;1:批次号)
         */
        @Schema(description = "物料管控粒度(0:单只序列号;1:批次号)")
        private Integer controlMaterialGranularity;

        /**
         * 批次号/序列号规则（正则表达式方式）
         */
        @Schema(description = "批次号/序列号规则（正则表达式方式）")
        private List<List<SnRuleDTO>> serialNumberRule;

        /**
         * 定义需要扫码多少次序列号，可不管控或指定数量（当管控粒度为单只，进行上料数量管控，为0不进行管控）
         */
        @Schema(description = "定义需要扫码多少次序列号，可不管控或指定数量（当管控粒度为单只，进行上料数量管控，为0不进行管控）")
        private Integer controlSnCount;

        /**
         * 可上料集合(主料及替换料作为一组)
         */
        private List<MaterialGroupInfo> materialGroupInfoList;
        /**
         * 是否为胶水
         */
        private Boolean isGlue;

        public Boolean getIsGlue() {
            return isGlue;
        }

        public MaterialRuleInfo setIsGlue(Boolean isGlue) {
            this.isGlue = isGlue;
            return this;
        }

        public Double getProportion() {
            return proportion;
        }

        public MaterialRuleInfo setProportion(Double proportion) {
            this.proportion = proportion;
            return this;
        }

        public Double getNumber() {
            return number;
        }

        public MaterialRuleInfo setNumber(Double number) {
            this.number = number;
            return this;
        }

        public Long getMaterialId() {
            return materialId;
        }

        public MaterialRuleInfo setMaterialId(Long materialId) {
            this.materialId = materialId;
            return this;
        }

        public String getMaterialName() {
            return materialName;
        }

        public MaterialRuleInfo setMaterialName(String materialName) {
            this.materialName = materialName;
            return this;
        }

        public String getMaterialCode() {
            return materialCode;
        }

        public MaterialRuleInfo setMaterialCode(String materialCode) {
            this.materialCode = materialCode;
            return this;
        }

        public Boolean isDeduct() {
            return isDeduct;
        }

        public MaterialRuleInfo setDeduct(Boolean deduct) {
            isDeduct = deduct;
            return this;
        }

        public Boolean isCheckMaterial() {
            return isCheckMaterial;
        }

        public MaterialRuleInfo setCheckMaterial(Boolean checkMaterial) {
            isCheckMaterial = checkMaterial;
            return this;
        }

        public Boolean isCheckMaterialBatch() {
            return isCheckMaterialBatch;
        }

        public MaterialRuleInfo setCheckMaterialBatch(Boolean checkMaterialBatch) {
            isCheckMaterialBatch = checkMaterialBatch;
            return this;
        }

        public Double getLeftNumber() {
            return leftNumber;
        }

        public MaterialRuleInfo setLeftNumber(Double leftNumber) {
            this.leftNumber = leftNumber;
            return this;
        }

        public List<MaterialGroupInfo> getMaterialGroupInfoList() {
            return materialGroupInfoList;
        }

        public MaterialRuleInfo setMaterialGroupInfoList(List<MaterialGroupInfo> materialGroupInfoList) {
            this.materialGroupInfoList = materialGroupInfoList;
            return this;
        }

        public Integer getControlMaterialGranularity() {
            return controlMaterialGranularity;
        }

        public MaterialRuleInfo setControlMaterialGranularity(Integer controlMaterialGranularity) {
            this.controlMaterialGranularity = controlMaterialGranularity;
            return this;
        }

        public List<List<SnRuleDTO>> getSerialNumberRule() {
            return serialNumberRule;
        }

        public MaterialRuleInfo setSerialNumberRule(List<List<SnRuleDTO>> serialNumberRule) {
            this.serialNumberRule = serialNumberRule;
            return this;
        }

        public Integer getControlSnCount() {
            return controlSnCount;
        }

        public MaterialRuleInfo setControlSnCount(Integer controlSnCount) {
            this.controlSnCount = controlSnCount;
            return this;
        }

        /**
         * 当前工序能上的物料组(包含替换料)
         */
        public static class MaterialGroupInfo{


            /**
             * 当前物料id
             */
            @JsonSerialize(using = ToStringSerializer.class)
            private Long materialId;

            /**
             * 当前物料名称
             */
            private String materialName;

            /**
             * 当前物料编码
             */
            private String materialCode;

            /**
             * 物料批次集合
             */
            private List<MaterialBatchInfo> materialBatchList;

            /**
             * 当前物料组内的各个物料批次及数量
             */
            public static class MaterialBatchInfo{
                /**
                 * 物料批次号
                 */
                private String materialBatch;
                /**
                 * 物料批次数量
                 */
                private Double number;

                public String getMaterialBatch() {
                    return materialBatch;
                }

                public MaterialBatchInfo setMaterialBatch(String materialBatch) {
                    this.materialBatch = materialBatch;
                    return this;
                }

                public Double getNumber() {
                    return number;
                }

                public MaterialBatchInfo setNumber(Double number) {
                    this.number = number;
                    return this;
                }
            }

            public Long getMaterialId() {
                return materialId;
            }

            public MaterialGroupInfo setMaterialId(Long materialId) {
                this.materialId = materialId;
                return this;
            }

            public String getMaterialName() {
                return materialName;
            }

            public MaterialGroupInfo setMaterialName(String materialName) {
                this.materialName = materialName;
                return this;
            }

            public String getMaterialCode() {
                return materialCode;
            }

            public MaterialGroupInfo setMaterialCode(String materialCode) {
                this.materialCode = materialCode;
                return this;
            }

            public List<MaterialBatchInfo> getMaterialBatchList() {
                return materialBatchList;
            }

            public MaterialGroupInfo setMaterialBatchList(List<MaterialBatchInfo> materialBatchList) {
                this.materialBatchList = materialBatchList;
                return this;
            }
        }
    }

    /**
     * 抽检信息
     */
    @Schema(description = "抽检信息")
    public static class RandomInspectGetInfo{
        /**
         * 类型(0,首检;1,巡检;2:末检;3;终检;4:抽检)
         */
        @Schema(description = "类型(0,首检;1,巡检;2:末检;3;终检;4:抽检)")
        private Integer category;

        /**
         * 抽检数量
         */
        @Schema(description = "抽检数量")
        private Integer checkNumber;

        /**
         * 允许最大不合格数
         */
        @Schema(description = "允许最大不合格")
        private Integer maxUnQualifiedNumber;

        /**
         * 抽检检测项目列表
         */
        @Schema(description = "抽检检测项目列表")
        private List<ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo> checkItemInfoList;

        public Integer getCategory() {
            return category;
        }

        public RandomInspectGetInfo setCategory(Integer category) {
            this.category = category;
            return this;
        }

        public Integer getCheckNumber() {
            return checkNumber;
        }

        public RandomInspectGetInfo setCheckNumber(Integer checkNumber) {
            this.checkNumber = checkNumber;
            return this;
        }

        public Integer getMaxUnQualifiedNumber() {
            return maxUnQualifiedNumber;
        }

        public RandomInspectGetInfo setMaxUnQualifiedNumber(Integer maxUnQualifiedNumber) {
            this.maxUnQualifiedNumber = maxUnQualifiedNumber;
            return this;
        }

        public List<ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo> getCheckItemInfoList() {
            return checkItemInfoList;
        }

        public RandomInspectGetInfo setCheckItemInfoList(List<ClientGetCheckInfoDTO.CheckRuleInfo.CheckItemInfo> checkItemInfoList) {
            this.checkItemInfoList = checkItemInfoList;
            return this;
        }
    }



    /**
     * FQC信息
     */
    public static class FqcInfo {

        /**
         * FQC抽检数量
         */
        private Integer fqcCheckNumber;

        /**
         * 最大不合格数
         */
        @Schema(description = "最大不合格数")
        private Integer maxUnQualifiedNumber;

        /**
         * 流程框图信息
         */
        private List<WorkFlowInfo> workFlowInfoList;

        public Integer getFqcCheckNumber() {
            return fqcCheckNumber;
        }

        public FqcInfo setFqcCheckNumber(Integer fqcCheckNumber) {
            this.fqcCheckNumber = fqcCheckNumber;
            return this;
        }

        public Integer getMaxUnQualifiedNumber() {
            return maxUnQualifiedNumber;
        }

        public FqcInfo setMaxUnQualifiedNumber(Integer maxUnQualifiedNumber) {
            this.maxUnQualifiedNumber = maxUnQualifiedNumber;
            return this;
        }

        public List<WorkFlowInfo> getWorkFlowInfoList() {
            return workFlowInfoList;
        }

        public FqcInfo setWorkFlowInfoList(List<WorkFlowInfo> workFlowInfoList) {
            this.workFlowInfoList = workFlowInfoList;
            return this;
        }
    }

    /**
     * 流程框图信息
     */
    public static class WorkFlowInfo {

        /**
         * 流程框图ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        private Long id;

        /**
         * 流程框图名称
         */
        private String name;

        /**
         * 流程框图编码
         */
        private String code;

        public WorkFlowInfo() {
        }

        public WorkFlowInfo(WorkFlow workFlow) {
            this.id = workFlow.getId();
            this.name = workFlow.getName();
            this.code = workFlow.getCode();
        }

        public Long getId() {
            return id;
        }

        public WorkFlowInfo setId(Long id) {
            this.id = id;
            return this;
        }

        public String getName() {
            return name;
        }

        public WorkFlowInfo setName(String name) {
            this.name = name;
            return this;
        }

        public String getCode() {
            return code;
        }

        public WorkFlowInfo setCode(String code) {
            this.code = code;
            return this;
        }
    }

    /**
     * 易损件规则信息
     */
    public static class PedigreeStepWearingPartGroupInfo {

        /**
         * 易损件类型名称
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "易损件类型id")
        private Long wearingPartGroupId;

        /**
         * 易损件类型名称
         */
        @Schema(description = "易损件类型名称")
        private String wearingPartGroupName;

        /**
         * 数量
         */
        @Schema(description = "数量")
        private Integer number;

        /**
         * 替换易损件列表规则信息
         */
        private List<PedigreeStepWearingPartGroupInfo> pedigreeStepWearingPartGroupExchangeInfoList;

        public PedigreeStepWearingPartGroupInfo() {
        }


        public PedigreeStepWearingPartGroupInfo(WearingPartExchange wearingPartExchange) {
            this.wearingPartGroupId = wearingPartExchange.getExchangeWearingPartGroup().getId();
            this.wearingPartGroupName = wearingPartExchange.getExchangeWearingPartGroup().getName();
        }

        public PedigreeStepWearingPartGroupInfo(Long wearingPartGroupId, String wearingPartGroupName, Integer number) {
            this.wearingPartGroupId = wearingPartGroupId;
            this.wearingPartGroupName = wearingPartGroupName;
            this.number = number;
        }

        public PedigreeStepWearingPartGroupInfo(ClientGetStepInfoDTO.PedigreeStepWearingPartGroupInfo sourceWearingPartGroupInfo) {
            this.wearingPartGroupId = sourceWearingPartGroupInfo.getWearingPartGroupId();
            this.wearingPartGroupName = sourceWearingPartGroupInfo.getWearingPartGroupName();
            this.number = sourceWearingPartGroupInfo.getNumber();
        }

        public Long getWearingPartGroupId() {
            return wearingPartGroupId;
        }

        public PedigreeStepWearingPartGroupInfo setWearingPartGroupId(Long wearingPartGroupId) {
            this.wearingPartGroupId = wearingPartGroupId;
            return this;
        }

        public String getWearingPartGroupName() {
            return wearingPartGroupName;
        }

        public PedigreeStepWearingPartGroupInfo setWearingPartGroupName(String wearingPartGroupName) {
            this.wearingPartGroupName = wearingPartGroupName;
            return this;
        }

        public Integer getNumber() {
            return number;
        }

        public PedigreeStepWearingPartGroupInfo setNumber(Integer number) {
            this.number = number;
            return this;
        }

        public List<PedigreeStepWearingPartGroupInfo> getPedigreeStepWearingPartGroupExchangeInfoList() {
            return pedigreeStepWearingPartGroupExchangeInfoList;
        }

        public PedigreeStepWearingPartGroupInfo setPedigreeStepWearingPartGroupExchangeInfoList(List<PedigreeStepWearingPartGroupInfo> pedigreeStepWearingPartGroupExchangeInfoList) {
            this.pedigreeStepWearingPartGroupExchangeInfoList = pedigreeStepWearingPartGroupExchangeInfoList;
            return this;
        }
    }

    @Schema(description = "设备校准提醒信息")
    public static class FacilityCalibrateWaringInfo{
        /**
         * 设备ID
         */
        @JsonSerialize(using = ToStringSerializer.class)
        @Schema(description = "设备ID")
        private Long facilityId;

        /**
         * 设备编码
         */
        @Schema(description = "设备编码")
        private String facilityCode;

        /**
         * 设备名称
         */
        @Schema(description = "设备名称")
        private String facilityName;

        /**
         * 校准程序，0：内校，1：外校
         */
        @Schema(description = "校准程序，0：内校，1：外校")
        private Integer process;

        /**
         * 是否已逾期
         */
        @Schema(description = "是否已逾期")
        private Boolean isExpired;

        /**
         * 下次校准时间
         */
        @Schema(description = "下次校准时间")
        private LocalDateTime nextCalibrateTime;

        public FacilityCalibrateWaringInfo() {

        }

        public FacilityCalibrateWaringInfo(CalibrateRule calibrateRule) {
            this.facilityId = calibrateRule.getFacilityId();
            this.facilityCode = calibrateRule.getFacilityDto().getCode();
            this.facilityName = calibrateRule.getFacilityDto().getName();
            this.process = calibrateRule.getProcess();
        }

        public Long getFacilityId() {
            return facilityId;
        }

        public FacilityCalibrateWaringInfo setFacilityId(Long facilityId) {
            this.facilityId = facilityId;
            return this;
        }

        public String getFacilityCode() {
            return facilityCode;
        }

        public FacilityCalibrateWaringInfo setFacilityCode(String facilityCode) {
            this.facilityCode = facilityCode;
            return this;
        }

        public String getFacilityName() {
            return facilityName;
        }

        public FacilityCalibrateWaringInfo setFacilityName(String facilityName) {
            this.facilityName = facilityName;
            return this;
        }

        public Boolean getIsExpired() {
            return isExpired;
        }

        public FacilityCalibrateWaringInfo setIsExpired(Boolean expired) {
            this.isExpired = expired;
            return this;
        }

        public LocalDateTime getNextCalibrateTime() {
            return nextCalibrateTime;
        }

        public FacilityCalibrateWaringInfo setNextCalibrateTime(LocalDateTime nextCalibrateTime) {
            this.nextCalibrateTime = nextCalibrateTime;
            return this;
        }

        public Integer getProcess() {
            return process;
        }

        public FacilityCalibrateWaringInfo setProcess(Integer process) {
            this.process = process;
            return this;
        }
    }

}
