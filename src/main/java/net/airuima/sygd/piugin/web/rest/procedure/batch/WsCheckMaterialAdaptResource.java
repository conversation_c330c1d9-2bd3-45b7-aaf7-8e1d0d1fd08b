package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.domain.procedure.material.WsCheckMaterial;
import net.airuima.domain.procedure.material.WsCheckMaterialDetail;
import net.airuima.dto.base.BaseDTO;
import net.airuima.dto.sync.SyncResultDTO;
import net.airuima.dto.sync.SyncWsCheckMaterialDTO;
import net.airuima.service.procedure.material.IWsCheckMaterialService;
import net.airuima.service.procedure.material.WsCheckMaterialService;
import net.airuima.sygd.piugin.service.procedure.WsCheckMaterialAdaptService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseContent;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单核料表Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "工单核料表Resource")
@RestController
@RequestMapping("/plugins/api/ws-check-materials")
@AuthorityRegion("工单物料管控")
public class WsCheckMaterialAdaptResource extends ProtectBaseResource<WsCheckMaterial> {

    @Autowired
    private IWsCheckMaterialService[] wsCheckMaterialServices;

    @Autowired
    private WsCheckMaterialAdaptService wsCheckMaterialAdaptService;




    /**
     * 工单核料单同步
     *
     * @param syncWsCheckMaterialDtoList 核料单同步数据
     * @return
     * <AUTHOR>
     * @date 2022/7/14
     */
    @Operation(summary = "工单核料单同步")
    @PreAuthorize("@sc.checkSecurity()")
    @PostMapping("/sync-ws-check-materials")
    public ResponseEntity<ResponseData<List<SyncResultDTO>>> syncWsCheckMaterial(@RequestBody List<SyncWsCheckMaterialDTO> syncWsCheckMaterialDtoList) {
        try {
            return ResponseData.ok(wsCheckMaterialAdaptService.syncWsCheckMaterial(syncWsCheckMaterialDtoList));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        if (StringUtils.isBlank(authority)) {
            return "";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_READ)) {
            return "浏览工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_CREATE)) {
            return "新建工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_UPDATE)) {
            return "修改工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_DELETE)) {
            return "删除工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_IMPORT)) {
            return "导入工单核料";
        } else if (authority.equals(this.entityName.toUpperCase() + Constants.AUTHORITY_EXPORT)) {
            return "导出工单核料";
        }
        return "";
    }

}
