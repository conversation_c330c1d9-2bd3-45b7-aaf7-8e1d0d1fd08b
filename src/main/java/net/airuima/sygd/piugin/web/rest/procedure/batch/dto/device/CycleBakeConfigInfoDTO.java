package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "温循放入工序的配置参数")
public class CycleBakeConfigInfoDTO implements Serializable {
    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    /**
     * 出入温度范围
     */
    @Schema(description = "出入温度范围")
    private String temperatureRange;

    /**
     * 高温范围
     */
    @Schema(description = "高温范围")
    private String highTemperatureRange;

    /**
     * 低温范围
     */
    @Schema(description = "低温范围")
    private String lowTemperatureRange;

    /**
     * 温循次数
     */
    @Schema(description = "温循次数")
    private Integer times;

    /**
     * 温循条件编码
     */
    @Schema(description = "温循条件编码")
    private String cycleBakeGroupCode;

    /**
     * 温循条件名称
     */
    @Schema(description = "温循条件名称")
    private String cycleBakeGroupName;

    /**
     * 是否接入自动设备
     */
    @Schema(description = "是否接入自动设备")
    private Boolean facilityIntegration;

    public Boolean getFacilityIntegration() {
        return facilityIntegration;
    }

    public CycleBakeConfigInfoDTO setFacilityIntegration(Boolean facilityIntegration) {
        this.facilityIntegration = facilityIntegration;
        return this;
    }

    public String getCycleBakeGroupCode() {
        return cycleBakeGroupCode;
    }

    public CycleBakeConfigInfoDTO setCycleBakeGroupCode(String cycleBakeGroupCode) {
        this.cycleBakeGroupCode = cycleBakeGroupCode;
        return this;
    }

    public String getCycleBakeGroupName() {
        return cycleBakeGroupName;
    }

    public CycleBakeConfigInfoDTO setCycleBakeGroupName(String cycleBakeGroupName) {
        this.cycleBakeGroupName = cycleBakeGroupName;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public CycleBakeConfigInfoDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public CycleBakeConfigInfoDTO setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }

    public String getHighTemperatureRange() {
        return highTemperatureRange;
    }

    public CycleBakeConfigInfoDTO setHighTemperatureRange(String highTemperatureRange) {
        this.highTemperatureRange = highTemperatureRange;
        return this;
    }

    public String getLowTemperatureRange() {
        return lowTemperatureRange;
    }

    public CycleBakeConfigInfoDTO setLowTemperatureRange(String lowTemperatureRange) {
        this.lowTemperatureRange = lowTemperatureRange;
        return this;
    }

    public int getTimes() {
        return times;
    }

    public CycleBakeConfigInfoDTO setTimes(int times) {
        this.times = times;
        return this;
    }
}
