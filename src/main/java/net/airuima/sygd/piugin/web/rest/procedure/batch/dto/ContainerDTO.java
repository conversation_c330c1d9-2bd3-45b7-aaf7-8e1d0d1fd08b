package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;

import java.io.Serializable;

/**
 * 容器DTO
 */
@Schema(description = "容器DTO")
public class ContainerDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器名称
     */
    @Schema(description = "容器名称")
    private String name;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String code;

    /**
     * 是否占用(0:否;1:是)
     */
    @Schema(description = "是否占用(0:否;1:是)")
    private boolean status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;

    /**
     * 子工单编码
     */
    @Schema(description = "子工单编码")
    private String  subWorkSheetSerialNumber;

    public String getName() {
        return name;
    }

    public ContainerDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ContainerDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public boolean isStatus() {
        return status;
    }

    public ContainerDTO setStatus(boolean status) {
        this.status = status;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ContainerDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ContainerDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public ContainerDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }
}
