package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;


import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器创建DTO
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Schema(description = "容器创建DTO")
public class ContainerCreateDTO {

    /**
     * 子工单编码
     */
    @Schema(description = "子工单编码")
    private String subWorkSheetSerialNumber;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;

    /**
     * 转容器前数量
     */
    @Schema(description = "转容器前数量")
    private Integer beforeChangeNumber;

    /**
     * 转容器后数量
     */
    @Schema(description = "转容器后数量")
    private Integer afterChangeNumber;


    public Integer getBeforeChangeNumber() {
        return beforeChangeNumber;
    }

    public ContainerCreateDTO setBeforeChangeNumber(Integer beforeChangeNumber) {
        this.beforeChangeNumber = beforeChangeNumber;
        return this;
    }

    public Integer getAfterChangeNumber() {
        return afterChangeNumber;
    }

    public ContainerCreateDTO setAfterChangeNumber(Integer afterChangeNumber) {
        this.afterChangeNumber = afterChangeNumber;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public ContainerCreateDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ContainerCreateDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }
}
