package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2021, 武汉睿码智能科技有限公司
 *
 * <AUTHOR>
 * @date 2021-05-23
 */
@Schema(description = "老化放入工序的配置参数")
public class AgeingConfigInfoDTO implements Serializable {

    /**
     * 时长范围
     */
    @Schema(description = "时长范围")
    private String durationRange;

    /**
     * 温度范围
     */
    @Schema(description = "温度范围")
    private String temperatureRange;

    /**
     * 电流范围
     */
    @Schema(description = "电流范围")
    private String electricRange;

    /**
     * 老化条件编码
     */
    @Schema(description = "老化条件编码")
    private String ageingGroupCode;

    /**
     * 老化条件名称
     */
    @Schema(description = "老化条件名称")
    private String ageingGroupName;

    /**
     * 是否接入自动设备
     */
    @Schema(description = "是否接入自动设备")
    private Boolean facilityIntegration;

    public Boolean getFacilityIntegration() {
        return facilityIntegration;
    }

    public AgeingConfigInfoDTO setFacilityIntegration(Boolean facilityIntegration) {
        this.facilityIntegration = facilityIntegration;
        return this;
    }

    public String getDurationRange() {
        return durationRange;
    }

    public AgeingConfigInfoDTO setDurationRange(String durationRange) {
        this.durationRange = durationRange;
        return this;
    }

    public String getTemperatureRange() {
        return temperatureRange;
    }

    public AgeingConfigInfoDTO setTemperatureRange(String temperatureRange) {
        this.temperatureRange = temperatureRange;
        return this;
    }

    public String getElectricRange() {
        return electricRange;
    }

    public AgeingConfigInfoDTO setElectricRange(String electricRange) {
        this.electricRange = electricRange;
        return this;
    }

    public String getAgeingGroupCode() {
        return ageingGroupCode;
    }

    public AgeingConfigInfoDTO setAgeingGroupCode(String ageingGroupCode) {
        this.ageingGroupCode = ageingGroupCode;
        return this;
    }

    public String getAgeingGroupName() {
        return ageingGroupName;
    }

    public AgeingConfigInfoDTO setAgeingGroupName(String ageingGroupName) {
        this.ageingGroupName = ageingGroupName;
        return this;
    }
}
