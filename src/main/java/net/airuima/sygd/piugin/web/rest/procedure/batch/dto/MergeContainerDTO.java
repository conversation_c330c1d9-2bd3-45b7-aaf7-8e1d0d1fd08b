package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 合并容器
 *
 * <AUTHOR>
 * @date 2024-09-18
 */
@Schema
public class MergeContainerDTO {

    /**
     * 转容器容器ID列表
     */
    @Schema(description = "转容器容器ID列表")
    private List<Long> idList;

    /**
     * 合并容器编码
     */
    @Schema(description = "合并容器编码")
    private String mergerContainerCode;

    public List<Long> getIdList() {
        return idList;
    }

    public MergeContainerDTO setIdList(List<Long> idList) {
        this.idList = idList;
        return this;
    }

    public String getMergerContainerCode() {
        return mergerContainerCode;
    }

    public MergeContainerDTO setMergerContainerCode(String mergerContainerCode) {
        this.mergerContainerCode = mergerContainerCode;
        return this;
    }
}
