package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标签数据查询DTO
 *
 * <AUTHOR>
 * @date 2024-12-06
 */
@Schema(description = "标签数据查询DTO")
public class LabelDataQueryDTO {

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String subWorkSheetSerialNumber;

    /**
     * 芯片编码项目规则
     */
    @Schema(description = "芯片编码项目规则")
    private List<String> chipList;

    @Schema(description = "变量列表")
    private List<String> varList;

    public List<String> getVarList() {
        return varList;
    }

    public LabelDataQueryDTO setVarList(List<String> varList) {
        this.varList = varList;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public LabelDataQueryDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public List<String> getChipList() {
        return chipList;
    }

    public LabelDataQueryDTO setChipList(List<String> chipList) {
        this.chipList = chipList;
        return this;
    }
}
