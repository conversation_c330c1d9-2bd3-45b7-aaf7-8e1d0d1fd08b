package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.sygd.piugin.domain.procedure.batch.WorkSheetContainer;
import net.airuima.sygd.piugin.service.procedure.batch.WorkSheetContainerService;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.web.ProtectBaseResource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单SN关联Resource
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Tag(name = "工单容器关联Resource")
@RestController
@RequestMapping("/plugins/api/work-sheet-containers")
@AuthorityRegion("生产工单")
public class WorkSheetContainerAdaptResource extends ProtectBaseResource<WorkSheetContainer> {

    private static final String MODULE = "工单容器预绑定";

    private final WorkSheetContainerService workSheetContainerService;

    public WorkSheetContainerAdaptResource(WorkSheetContainerService workSheetContainerService) {
        this.workSheetContainerService = workSheetContainerService;
        this.mapUri = "/plugins/api/work-sheet-containers";
    }



    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }
}
