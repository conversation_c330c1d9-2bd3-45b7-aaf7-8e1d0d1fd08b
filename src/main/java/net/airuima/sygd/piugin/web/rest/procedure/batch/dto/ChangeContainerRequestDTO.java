package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转容器请求
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Schema(description = "转容器请求")
public class ChangeContainerRequestDTO {

    /**
     * 子工单ID
     */
    @Schema(description = "子工单ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subWorkSheetId;

    /**
     * 原容器ID
     */
    @Schema(description = "原容器ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long originContainerId;

    /**
     * 容器ID
     */
    @Schema(description = "容器ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long containerId;


    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;


    /**
     * 剩余数量
     */
    @Schema(description = "剩余数量")
    private Integer remainNumber;


    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long workCellId;


    /***
     * 工序ID
     */
    @Schema(description = "工序ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long stepId;


    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    public Long getStepId() {
        return stepId;
    }

    public ChangeContainerRequestDTO setStepId(Long stepId) {
        this.stepId = stepId;
        return this;
    }

    public Integer getRemainNumber() {
        return remainNumber;
    }

    public ChangeContainerRequestDTO setRemainNumber(Integer remainNumber) {
        this.remainNumber = remainNumber;
        return this;
    }

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public ChangeContainerRequestDTO setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
        return this;
    }

    public Long getContainerId() {
        return containerId;
    }

    public ChangeContainerRequestDTO setContainerId(Long containerId) {
        this.containerId = containerId;
        return this;
    }

    public Long getOriginContainerId() {
        return originContainerId;
    }

    public ChangeContainerRequestDTO setOriginContainerId(Long originContainerId) {
        this.originContainerId = originContainerId;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ChangeContainerRequestDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Long getWorkCellId() {
        return workCellId;
    }

    public ChangeContainerRequestDTO setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public ChangeContainerRequestDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }
}
