package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.sygd.piugin.service.procedure.LabelDataService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDataDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDataQueryDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.LabelDataQueryDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.LabelDataResponseDTO;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 标签数据
 *
 * <AUTHOR>
 * @date 2024-12-03
 */
@Schema(name = "标签数据")
@RestController
@RequestMapping("/plugins/api/label-datas")
public class LabelDataResource {

    private final LabelDataService labelDataService;

    public LabelDataResource(LabelDataService labelDataService) {
        this.labelDataService = labelDataService;
    }

    /**
     * 获取标签信息
     *
     * @param labelDataQueryDTO 标签数据查询DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < LabelDataResponseDTO>> 标签数据响应DTO
     */
    @PostMapping({"/info"})
    public ResponseEntity<ResponseData<LabelDataResponseDTO>> getLabelInfo(@RequestBody LabelDataQueryDTO labelDataQueryDTO) throws Exception {
        try {
            LabelDataResponseDTO labelDataResponseDTO = labelDataService.getLabelInfo(labelDataQueryDTO);
            return ResponseData.ok(labelDataResponseDTO);
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 获取容器信息
     *
     * @param containerDataQueryDTO 容器数据查询DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < ContainerDataDTO>> 容器数据DTO
     */
    @PostMapping({"/container"})
    public ResponseEntity<ResponseData<ContainerDataDTO>> getContainer(@RequestBody ContainerDataQueryDTO containerDataQueryDTO) throws Exception {
        try {
            return ResponseData.ok(labelDataService.getContainer(containerDataQueryDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 获取打印容器信息
     *
     * @param containerDataQueryDTO 容器数据查询DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < ContainerDataDTO>> 容器数据DTO
     */
    @PostMapping({"/print/container"})
    public ResponseEntity<ResponseData<ContainerDataDTO>> getPrintContainer(@RequestBody ContainerDataQueryDTO containerDataQueryDTO) throws Exception {
        try {
            return ResponseData.ok(labelDataService.getPrintContainer(containerDataQueryDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }
}
