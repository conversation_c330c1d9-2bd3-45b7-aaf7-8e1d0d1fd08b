package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工艺流程单请求参数
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Schema(description = "工艺流程单请求参数")
public class WorkFlowOrderDTO {

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String subWorkSheetSerialNumber;

    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long operatorId;

    public Long getOperatorId() {
        return operatorId;
    }

    public WorkFlowOrderDTO setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public WorkFlowOrderDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }
}
