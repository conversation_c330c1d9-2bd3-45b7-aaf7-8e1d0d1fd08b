package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.procedure.aps.SubWorkSheet;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器数据
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Schema(name = "容器数据")
public class ContainerDataDTO extends CustomBaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 容器名称
     */
    @Schema(description = "容器名称")
    private String name;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String code;

    /**
     * 是否占用(0:否;1:是)
     */
    @Schema(description = "是否占用(0:否;1:是)")
    private boolean status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 子工单
     */
    @Schema(description = "子工单")
    private SubWorkSheet subWorkSheet;

    private ContainerDetailDataDTO containerDetail;

    public ContainerDetailDataDTO getContainerDetail() {
        return containerDetail;
    }

    public void setContainerDetail(ContainerDetailDataDTO containerDetail) {
        this.containerDetail = containerDetail;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public void setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
    }
}
