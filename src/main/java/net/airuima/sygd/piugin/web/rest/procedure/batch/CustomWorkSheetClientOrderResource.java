package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.domain.base.process.WorkFlowConvertConfig;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder;
import net.airuima.sygd.piugin.service.procedure.CustomWorkSheetClientOrderService;
import net.airuima.sygd.piugin.service.procedure.CustomWorkSheetClientService;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单销售订单Resource
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Tag(name = "工单销售订单Resource")
@RestController
@RequestMapping("/plugins/api/work-sheet-orders")
public class CustomWorkSheetClientOrderResource extends ProtectBaseResource<CustomWorkSheetClientOrder> {


    private final CustomWorkSheetClientOrderService customWorkSheetClientOrderService;

    public CustomWorkSheetClientOrderResource(CustomWorkSheetClientOrderService customWorkSheetClientOrderService) {
        this.customWorkSheetClientOrderService = customWorkSheetClientOrderService;
    }

    /**
     * 重写新增函数
     *
     * @param entity 工单客户订单
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder> 工单客户订单
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<CustomWorkSheetClientOrder> create(@Valid @RequestBody CustomWorkSheetClientOrder entity) throws URISyntaxException {
        try {
            customWorkSheetClientOrderService.saveWorkSheetClientOrder(entity);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }


    /**
     * 重写新增函数
     *
     * @param entityList 工单客户订单
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder> 工单客户订单
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/batch")
    public ResponseEntity<CustomWorkSheetClientOrder> batchCreate(@Valid @RequestBody List<CustomWorkSheetClientOrder> entityList) throws URISyntaxException {
        try {
            customWorkSheetClientOrderService.bathSaveWorkSheetClientOrder(entityList);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }



    /**
     * 重写更新函数，
     *
     * @param entity 工单客户订单
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.process.CustomWorkSheetClientOrder> 工单客户订单
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<CustomWorkSheetClientOrder> update(@Valid @RequestBody CustomWorkSheetClientOrder entity) throws URISyntaxException {
        try {
            customWorkSheetClientOrderService.saveWorkSheetClientOrder(entity);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 删除工单客户订单
     *
     * @param id 主键
     * @return org.springframework.http.ResponseEntity<Void>  响应
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        customWorkSheetClientOrderService.deleteEntity(Arrays.asList(id));
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(CustomWorkSheetClientOrder.class.getSimpleName()), "")).build();
    }

    /**
     * 删除工单客户订单
     *
     * @param ids 主键
     * @return org.springframework.http.ResponseEntity<Void>  响应
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/batch/delete"})
    public ResponseEntity<Void> batchDelete(@RequestBody List<Long> ids) {
        customWorkSheetClientOrderService.deleteEntity(ids);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(CustomWorkSheetClientOrder.class.getSimpleName()), "")).build();
    }

    /**
     * 根据工单编码查询工单客户订单
     *
     * @param serialNumber 工单编码
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClientOrder> 工单客户订单
     */
    @Operation(summary = "根据工单编码查询工单客户订单")
    @GetMapping("/worksheet")
    public ResponseEntity<ResponseData<List<CustomWorkSheetClientOrder>>> findByWorkSheet(@RequestParam(name = "serialNumber") String serialNumber) {
        return ResponseData.ok(customWorkSheetClientOrderService.findByWorkSheet(serialNumber));
    }

}
