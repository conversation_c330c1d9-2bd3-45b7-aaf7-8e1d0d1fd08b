package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.constant.Constants;

import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device.ClientSaveStepInfoDTO;
import net.airuima.dto.client.base.BaseClientDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDTO;
import net.airuima.sygd.piugin.service.procedure.batch.ClientStepAdaptService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueBatchRequestDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MesDataRequestDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MesDataResponseDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device.ClientGetStepInfoDTO;
import net.airuima.util.*;
import net.airuima.web.rest.client.dto.ClientContainerRollBakeDTO;
import net.airuima.web.rest.error.NumberDeductException;
import net.airuima.web.rest.error.SaveRepeatException;
import net.airuima.web.rest.errors.BadRequestAlertException;
import net.airuima.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * RWork工序相关Resource
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Tag(name = "Rworker-Client工序相关Resource")
@RestController
@RequestMapping("/plugins/api/client/steps")
public class ClientStepAdaptResource {

    private final ClientStepAdaptService clientStepAdaptService;
    private final RedisUtils redisUtils;
    private static final Logger log = LoggerFactory.getLogger(ClientStepAdaptResource.class);

    public ClientStepAdaptResource(ClientStepAdaptService clientStepAdaptService, RedisUtils redisUtils) {
        this.clientStepAdaptService = clientStepAdaptService;
        this.redisUtils = redisUtils;
    }

    /**
     * Rwokrer调用容器回退
     *
     * @param rollBackDto 回退DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseContent < ClientContainerRollBakeDTO>> 容器回退详情
     */
    @Operation(summary = "Rwokrer调用容器回退")
    @PostMapping("/rollBake-container")
    @PreventRepeatSubmit(expireTime = 10)
    public ResponseEntity<ResponseContent<ClientContainerRollBakeDTO>> rollBakeContainerDetail(@RequestBody RollBackDTO rollBackDto) {
        try {
            ClientContainerRollBakeDTO clientContainerRollBakeDto = clientStepAdaptService.rollBakeContainerDetail(rollBackDto);
            return ResponseContent.isOk(clientContainerRollBakeDto);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 获取工序信息
     *
     * @param stepInfo 请求传过来的工序信息
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseContent < ClientGetStepInfoDTO>> 工序信息
     */
    @Operation(summary = "获取工序信息")
    @PostMapping("/get-step-info")
    public ResponseEntity<ResponseContent<ClientGetStepInfoDTO>> getStepInfo(@RequestBody ClientGetStepInfoDTO.StepInfo stepInfo) {
        try {
            return ResponseContent.isOk(clientStepAdaptService.getStepInfo(stepInfo));
        } catch (BadRequestAlertException e) {
            log.info("获取工序信息失败:{}", e.getMessage());
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(e.getEntityName(), e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException e) {
            log.info("获取工序信息失败:{}", e.getMessage());
            return ResponseContent.isOk(new ClientGetStepInfoDTO(new BaseClientDTO(Constants.KO, e.getErrorMessage())));
        } catch (Exception e) {
            log.info("获取工序信息失败:{}", e.getMessage());
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 保存工序信息
     *
     * @param clientSaveStepInfoDto 待保存的工序信息
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseContent < BaseClientDTO>> 保存结果
     */
    @Operation(summary = "保存工序信息")
    @PostMapping("/save-step-info")
    @PreventRepeatSubmit(expireTime = 10)
    public ResponseEntity<ResponseContent<BaseClientDTO>> saveStepInfo(@RequestBody ClientSaveStepInfoDTO clientSaveStepInfoDto) {
        try {
            return ResponseContent.isOk(clientStepAdaptService.saveStepInfo(clientSaveStepInfoDto));
        } catch (NumberDeductException numberDeductException) {
            log.info("保存工序信息失败:{}", numberDeductException.getMessage());
            return ResponseContent.isOk(new BaseClientDTO(Constants.KO, numberDeductException.getMessage()));
        } catch (SaveRepeatException e) {
            log.info("保存工序信息失败:{}", e.getMessage());
            return ResponseContent.isOk(new BaseClientDTO(Constants.KO, "当前生产数据已保存,请勿重复提交!"));
        } catch (BadRequestAlertException e) {
            log.info("保存工序信息失败:{}", e.getMessage());
            return ResponseContent.isOk(new BaseClientDTO(Constants.KO, e.getTitle()));
        } catch (Exception e) {
            e.printStackTrace();
            log.info("保存工序信息失败:{}", e.getMessage());
            if (StringUtils.isNotBlank(clientSaveStepInfoDto.getXsrfToken())) {
                redisUtils.del(clientSaveStepInfoDto.getXsrfToken());
            }
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }


    /**
     * 查询MES数据
     *
     * @param mesDataRequestList MES数据请求
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseContent < java.util.List < MesDataResponseDTO>>> MES数据
     */
    @PostMapping("/query-mes-data")
    public ResponseEntity<ResponseContent<List<MesDataResponseDTO>>> queryMesData(@RequestBody List<MesDataRequestDTO> mesDataRequestList) {
        try {
            return ResponseContent.isOk(clientStepAdaptService.queryMesData(mesDataRequestList));
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(e.getEntityName(), e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException e) {
            return ResponseContent.isOk(null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseContent.badRequest().message(e.toString()).isBadRequestBuild();
        }
    }

    /**
     * 验证单组分胶的合法性
     *
     * @param glueBatchRequestDTO 验证参数
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < net.airuima.sygd.piugin.web.rest.procedure.batch.dto.GlueMaterialBatchVerifyDTO>>  验证结果
     */
    @PostMapping("/validate-glue")
    @Operation(description = "验证单组分胶的合法性")
    public GlueMaterialBatchVerifyDTO validateGlue(@RequestBody GlueBatchRequestDTO glueBatchRequestDTO) {
        try {
            return clientStepAdaptService.validateGlue(glueBatchRequestDTO.getCode());
        } catch (Exception e) {
            log.info("验证单组分胶的合法性失败:{}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }
}
