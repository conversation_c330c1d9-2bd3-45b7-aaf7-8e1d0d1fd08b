package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.sygd.piugin.domain.procedure.batch.ChangeContainer;
import net.airuima.sygd.piugin.service.procedure.batch.ChangeContainerService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ChangeContainerRequestDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.ContainerRemainDTO;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.MergeContainerDTO;
import net.airuima.util.AuthorityDescriptionUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 转换容器Resource
 *
 * <AUTHOR>
 * @date 2024-09-14
 */
@Tag(name = "转换容器Resource")
@RestController
@RequestMapping("/plugins/api/change-containers")
@AuthorityRegion("生产工单")
public class ChangeContainerAdaptResource extends ProtectBaseResource<ChangeContainer> {

    private static final String MODULE = "转换容器";

    private final ChangeContainerService changeContainerService;

    public ChangeContainerAdaptResource(ChangeContainerService changeContainerService) {
        this.changeContainerService = changeContainerService;
        this.mapUri = "/plugins/api/change-containers";
    }

    /**
     * 转换容器
     *
     * @param changeContainerRequestDTOList 转换容器请求
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < Void>>  通用响应
     */
    @Operation(summary = "转换容器")
    @PostMapping("/change")
    public ResponseEntity<ResponseData<Boolean>> changeContainer(@RequestBody List<ChangeContainerRequestDTO> changeContainerRequestDTOList) {
        try {
            return ResponseData.ok(changeContainerService.changeContainer(changeContainerRequestDTOList));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据工位id获取容器信息
     *
     * @param id 工位id
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < ContainerRemainDTO>>> 容器信息
     */
    @Operation(summary = "根据工位id获取容器信息")
    @GetMapping("/work-cell/{id}")
    public ResponseEntity<ResponseData<List<ContainerRemainDTO>>> getContainerByWorkCell(@PathVariable("id") Long id) {
        try {
            return ResponseData.ok(changeContainerService.getContainerByWorkCell(id));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 根据可用的待合并容器信息
     *
     * @param stepId         工序id
     * @param subWorksheetId 子工单id
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < ContainerRemainDTO>>> 容器信息
     */
    @Operation(summary = "根据可用的待合并容器信息")
    @GetMapping("/remain")
    public ResponseEntity<ResponseData<List<ContainerRemainDTO>>> getContainerByWorkCellAndWorkSheet(@RequestParam("stepId") Long stepId, @RequestParam("subWorksheetId") Long subWorksheetId) {
        try {
            return ResponseData.ok(changeContainerService.getMergeContainer(stepId, subWorksheetId));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }

    /**
     * 合并容器
     *
     * @param mergeContainerDTO 合并容器请求
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.lang.Boolean>>  通用响应
     */
    @Operation(summary = "合并容器")
    @PostMapping("/merge")
    public ResponseEntity<ResponseData<Boolean>> mergeContainer(@RequestBody MergeContainerDTO mergeContainerDTO) {
        try {
            return ResponseData.ok(changeContainerService.mergeContainer(mergeContainerDTO));
        } catch (ResponseException e) {
            return ResponseData.error(e);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


    @Override
    public String getAuthorityDescription(String authority) {
        return AuthorityDescriptionUtil.getAuthorityDescription(this.entityName, authority, MODULE);
    }

}
