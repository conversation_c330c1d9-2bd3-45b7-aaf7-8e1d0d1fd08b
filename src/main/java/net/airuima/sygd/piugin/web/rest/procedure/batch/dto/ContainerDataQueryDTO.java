package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器数据查询
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Schema(name = "容器数据查询DTO")
public class ContainerDataQueryDTO {

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String code;

    /**
     * 绑定状态
     */
    @Schema(description = "绑定状态")
    private Boolean bind;

    public Boolean getBind() {
        return bind;
    }

    public void setBind(Boolean bind) {
        this.bind = bind;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
