package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * MES数据响应DTO
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Schema(description = "MES数据响应DTO")
public class MesDataResponseDTO {

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;


    @Schema(description = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    private JSONObject data;

    public JSONObject getData() {
        return data;
    }

    public MesDataResponseDTO setData(JSONObject data) {
        this.data = data;
        return this;
    }

    public String getCode() {
        return code;
    }

    public MesDataResponseDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Long getId() {
        return id;
    }

    public MesDataResponseDTO setId(Long id) {
        this.id = id;
        return this;
    }
}
