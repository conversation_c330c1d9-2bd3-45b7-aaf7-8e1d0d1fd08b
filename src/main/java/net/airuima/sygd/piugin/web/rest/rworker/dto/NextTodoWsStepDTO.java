package net.airuima.sygd.piugin.web.rest.rworker.dto;

import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "获取待做工序DTO")
public class NextTodoWsStepDTO {

    /**
     * 工位id
     */
    @Schema(description = "工位id")
    private Long workCellId;

    /**
     * 员工id
     */
    @Schema(description = "员工id")
    private Long staffId;

    /**
     * 生产工单id
     */
    @Schema(description = "生产工单id")
    private Long productWorkSheetId;

    /**
     * 请求生产的容器编码
     */
    @Schema(description = "请求生产的容器编码")
    private String requestContainerCode;

    /**
     * 请求生产的SN
     */
    @Schema(description = "请求生产的SN")
    private String sn;

    /**
     * 类型（0：工单；1：容器；2：sn）
     */
    @Schema(description = "类型（0：工单；1：容器；2：sn）")
    private Integer category;

    public Long getWorkCellId() {
        return workCellId;
    }

    public void setWorkCellId(Long workCellId) {
        this.workCellId = workCellId;
    }

    public Long getStaffId() {
        return staffId;
    }

    public void setStaffId(Long staffId) {
        this.staffId = staffId;
    }

    public Long getProductWorkSheetId() {
        return productWorkSheetId;
    }

    public void setProductWorkSheetId(Long productWorkSheetId) {
        this.productWorkSheetId = productWorkSheetId;
    }

    public String getRequestContainerCode() {
        return requestContainerCode;
    }

    public void setRequestContainerCode(String requestContainerCode) {
        this.requestContainerCode = requestContainerCode;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Integer getCategory() {
        return category;
    }

    public void setCategory(Integer category) {
        this.category = category;
    }
}
