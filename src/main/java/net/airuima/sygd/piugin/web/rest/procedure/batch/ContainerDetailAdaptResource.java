package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import net.airuima.config.annotation.AuthSkip;
import net.airuima.config.annotation.AuthorityRegion;
import net.airuima.config.annotation.FuncInterceptor;
import net.airuima.constant.Constants;
import net.airuima.domain.procedure.batch.ContainerDetail;
import net.airuima.service.procedure.batch.ContainerDetailService;
import net.airuima.service.procedure.batch.api.IRollbackStepService;
import net.airuima.service.procedure.batch.dto.ContainerDetailReplaceDTO;
import net.airuima.sygd.piugin.service.procedure.batch.RollbackStepAdaptService;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.procedure.batch.dto.ContainerDetailDTO;
import net.airuima.web.rest.procedure.batch.dto.RollBackDTO;
import net.airuima.xsrf.interceptor.PreventRepeatSubmit;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器生产详情Resource
 *
 * <AUTHOR>
 * @date 2021-01-08
 */
@Tag(name = "容器生产详情Resource")
@RestController
@RequestMapping("/plugins/api/container-details")
@AuthorityRegion("生产过程数据")
@FuncInterceptor("Container")
@AuthSkip("C")
public class ContainerDetailAdaptResource extends ProtectBaseResource<ContainerDetail> {

    private final ContainerDetailService containerDetailService;

    @Autowired
    private RollbackStepAdaptService rollbackStepAdaptService;

    public ContainerDetailAdaptResource(ContainerDetailService containerDetailService) {
        this.containerDetailService = containerDetailService;
        this.mapUri = "/plugins/api/container-details";
    }

    /**
     * 重写容器想起通用删除接口
     *
     * @param rollBackDto 回退参数DTO
     * @return org.springframework.http.ResponseEntity<net.airuima.util.ResponseData < java.util.List < net.airuima.domain.procedure.batch.ContainerDetail>>>   返回结果
     * <AUTHOR>
     * @date 2021-08-27
     **/
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @Operation(summary = "通过容器工序详情ID删除记录")
    @PostMapping("/rollback-container")
    @PreventRepeatSubmit
    public ResponseEntity<ResponseData<List<ContainerDetail>>> rollbackContainer(@RequestBody RollBackDTO rollBackDto) {
        try {
            ContainerDetailReplaceDTO containerDetailReplaceDto = rollbackStepAdaptService.deleteContainerDetailById(rollBackDto);
            if (Constants.KO.equals(containerDetailReplaceDto.getStatus())) {
                return ResponseData.error("Rollback", containerDetailReplaceDto.getMessage());
            } else {
                return ResponseData.ok(containerDetailReplaceDto.getContainerDetailList(), "success", "工序回退成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseData.error(e);
        }
    }


}
