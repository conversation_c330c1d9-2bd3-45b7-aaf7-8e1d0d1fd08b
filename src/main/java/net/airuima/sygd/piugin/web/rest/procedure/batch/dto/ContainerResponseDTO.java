package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import net.airuima.domain.base.CustomBaseEntity;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器信息DTO
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Schema(description = "容器信息DTO")
public class ContainerResponseDTO extends CustomBaseEntity implements Serializable {
    /**
     * 容器名称
     */
    @Schema(description = "容器名称")
    @Column(name = "name")
    private String name;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    @Column(name = "code")
    private String code;

    /**
     * 是否占用(0:否;1:是)
     */
    @Schema(description = "是否占用(0:否;1:是)")
    @Column(name = "status")
    private Boolean status;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @Column(name = "note")
    private String note;

    public String getName() {
        return name;
    }

    public ContainerResponseDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public ContainerResponseDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Boolean getStatus() {
        return status;
    }

    public ContainerResponseDTO setStatus(Boolean status) {
        this.status = status;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ContainerResponseDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
