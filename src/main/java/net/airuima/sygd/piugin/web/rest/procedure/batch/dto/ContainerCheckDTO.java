package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 容器校验DTO
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Schema(description = "容器校验DTO")
public class ContainerCheckDTO {

    /**
     * 子工单id
     */
    @Schema(description = "子工单id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long subWorkSheetId;

    /**
     * 容器编码
     */
    @Schema(description = "容器编码")
    private String containerCode;

    public ContainerCheckDTO() {
    }

    public ContainerCheckDTO(Long subWorkSheetId, String containerCode) {
        this.subWorkSheetId = subWorkSheetId;
        this.containerCode = containerCode;
    }

    public Long getSubWorkSheetId() {
        return subWorkSheetId;
    }

    public ContainerCheckDTO setSubWorkSheetId(Long subWorkSheetId) {
        this.subWorkSheetId = subWorkSheetId;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public ContainerCheckDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }
}
