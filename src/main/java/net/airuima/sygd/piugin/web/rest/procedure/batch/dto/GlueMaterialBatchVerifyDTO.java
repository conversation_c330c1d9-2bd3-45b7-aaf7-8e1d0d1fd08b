package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水物料批次验证
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Schema(description = "胶水物料批次验证DTO")
public class GlueMaterialBatchVerifyDTO {

    @Schema(description = "数据")
    private GlueMaterialBatchVerifyDataDTO data;

    @Schema(description = "key")
    private String key;

    @Schema(description = "消息")
    private String message;

    public String getMessage() {
        return message;
    }

    public GlueMaterialBatchVerifyDTO setMessage(String message) {
        this.message = message;
        return this;
    }

    public GlueMaterialBatchVerifyDataDTO getData() {
        return data;
    }

    public GlueMaterialBatchVerifyDTO setData(GlueMaterialBatchVerifyDataDTO data) {
        this.data = data;
        return this;
    }

    public String getKey() {
        return key;
    }

    public GlueMaterialBatchVerifyDTO setKey(String key) {
        this.key = key;
        return this;
    }
}
