package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.base.process.WorkFlow;
import net.airuima.domain.base.scene.WorkLine;
import net.airuima.domain.procedure.aps.WorkSheet;

import java.time.LocalDateTime;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 子工单
 *
 * <AUTHOR>
 * @date 2025-03-28
 */
@Schema(name = "子工单数据")
public class SubWorkSheetDataDTO extends CustomBaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 总工单
     */
    @Schema(description = "总工单")
    private WorkSheet workSheet;

    /**
     * 生产线
     */
    @Schema(description = "生产线")
    private WorkLine workLine;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String serialNumber;

    /**
     * 乐观锁版本
     */
    @Schema(description = "乐观锁版本")
    private Long version;

    /**
     * 流程框图
     */
    @Schema(description = "流程框图")
    private WorkFlow workFlow;

    /**
     * 投产数
     */
    @Schema(description = "投产数")
    private Integer number;

    /**
     * 合格数
     */
    @Schema(description = "合格数")
    private Integer qualifiedNumber;

    /**
     * 不合格数
     */
    @Schema(description = "不合格数")
    private Integer unqualifiedNumber;

    /**
     * 在线返修合格数
     */
    @Schema(description = "在线返修合格数")
    private Integer reworkQualifiedNumber;

    /**
     * 工序个数
     */
    @Schema(description = "工序个数")
    private Integer stepNumber;

    /**
     * 工序完成个数
     */
    @Schema(description = "工序完成个数")
    private Integer stepCompNumber;

    /**
     * 计划开工日期
     */
    @Schema(description = "计划开工日期")
    private LocalDateTime planStartDate;

    /**
     * 计划结单日期
     */
    @Schema(description = "计划结单日期")
    private LocalDateTime planEndDate;

    /**
     * 实际开工日期
     */
    @Schema(description = "实际开工日期")
    private LocalDateTime actualStartDate;

    /**
     * 实际完成日期
     */
    @Schema(description = "实际完成日期")
    private LocalDateTime actualEndDate;

    /**
     * 工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)
     */
    @Schema(description = "工单状态(0:已下单;1:投产中;2:已暂停;3:已完成;4:正常结单;5:异常结单)", required = true)
    private Integer status;

    /**
     * erp 上传状态(0:未上传,1：已上传)
     */
    @Schema(description = "erp 上传状态(0:未上传,1：已上传)")
    private Integer syncStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;

    /**
     * 结单原因
     */
    @Schema(description = "结单原因")
    private String statementReason;

    /**
     * 降级总数
     */
    @Schema(description = "降级总数")
    private Integer downGradeNumber;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Integer priority;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public void setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
    }

    public WorkLine getWorkLine() {
        return workLine;
    }

    public void setWorkLine(WorkLine workLine) {
        this.workLine = workLine;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    public WorkFlow getWorkFlow() {
        return workFlow;
    }

    public void setWorkFlow(WorkFlow workFlow) {
        this.workFlow = workFlow;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getQualifiedNumber() {
        return qualifiedNumber;
    }

    public void setQualifiedNumber(Integer qualifiedNumber) {
        this.qualifiedNumber = qualifiedNumber;
    }

    public Integer getUnqualifiedNumber() {
        return unqualifiedNumber;
    }

    public void setUnqualifiedNumber(Integer unqualifiedNumber) {
        this.unqualifiedNumber = unqualifiedNumber;
    }

    public Integer getReworkQualifiedNumber() {
        return reworkQualifiedNumber;
    }

    public void setReworkQualifiedNumber(Integer reworkQualifiedNumber) {
        this.reworkQualifiedNumber = reworkQualifiedNumber;
    }

    public Integer getStepNumber() {
        return stepNumber;
    }

    public void setStepNumber(Integer stepNumber) {
        this.stepNumber = stepNumber;
    }

    public Integer getStepCompNumber() {
        return stepCompNumber;
    }

    public void setStepCompNumber(Integer stepCompNumber) {
        this.stepCompNumber = stepCompNumber;
    }

    public LocalDateTime getPlanStartDate() {
        return planStartDate;
    }

    public void setPlanStartDate(LocalDateTime planStartDate) {
        this.planStartDate = planStartDate;
    }

    public LocalDateTime getPlanEndDate() {
        return planEndDate;
    }

    public void setPlanEndDate(LocalDateTime planEndDate) {
        this.planEndDate = planEndDate;
    }

    public LocalDateTime getActualStartDate() {
        return actualStartDate;
    }

    public void setActualStartDate(LocalDateTime actualStartDate) {
        this.actualStartDate = actualStartDate;
    }

    public LocalDateTime getActualEndDate() {
        return actualEndDate;
    }

    public void setActualEndDate(LocalDateTime actualEndDate) {
        this.actualEndDate = actualEndDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(Integer syncStatus) {
        this.syncStatus = syncStatus;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getStatementReason() {
        return statementReason;
    }

    public void setStatementReason(String statementReason) {
        this.statementReason = statementReason;
    }

    public Integer getDownGradeNumber() {
        return downGradeNumber;
    }

    public void setDownGradeNumber(Integer downGradeNumber) {
        this.downGradeNumber = downGradeNumber;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }
}