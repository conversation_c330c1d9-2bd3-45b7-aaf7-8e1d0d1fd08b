package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.bom.MaterialDTO;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水配置DTO
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Schema(description = "胶水配置DTO")
public class GlueConfigDTO {

    /**
     * 物料id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "物料id")
    private Long materialId;


    private MaterialDTO materialDto = new MaterialDTO();

    /**
     * 组分(0:单组分;1:多组分)
     */
    @Schema(description = "组分(0:单组分;1:多组分)")
    private Integer category;

    /**
     * 有效期(H)
     */
    @Schema(description = "有效期(H)")
    private Double expireHour;

    /**
     * 回温时长(H)
     */
    @Schema(description = "回温时长(H)")
    private Double rewarmingHour;

    /**
     * 调胶比例
     * {
     * "material_id": material_id, //原胶水物料ID
     * "material_code": material_code, //原胶水物料编码
     * "rate": rate //该胶水比例，只允许填写整数，具体比例关系由客户自己定义值
     * }
     */
    @Schema(description = "调胶比例")
    private JSONArray rate;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String note;

    /**
     * 存储有效期（H）
     */
    @Schema(description = "存储有效期（H）")
    private Double storageExpireHour;

    /**
     * 脱泡时长（H）
     */
    @Schema(description = "脱泡时长（H）")
    private Double deaerationHour;

    /**
     * 质量保质期（H）
     */
    @Schema(description = "质量保质期（H）")
    private Double qualityExpireHour;

    /**
     * 回收时长(H)
     */
    @Schema(description = "回收时长(H)")
    private Double recoveryHour;

    /**
     * 最大回温次数
     */
    @Schema(description = "最大回温次数")
    private Integer maxRewarmingTimes;

    /**
     * 胶水组分数
     */
    @Schema(description = "胶水组分数")
    private Integer componentNumber;


    @Schema(description = "胶水工艺路线id")
    private GlueWorkFlowDTO glueWorkFlow;

    public Long getMaterialId() {
        return materialId;
    }

    public GlueConfigDTO setMaterialId(Long materialId) {
        this.materialId = materialId;
        return this;
    }

    public MaterialDTO getMaterialDto() {
        return materialDto;
    }

    public GlueConfigDTO setMaterialDto(MaterialDTO materialDto) {
        this.materialDto = materialDto;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public GlueConfigDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Double getExpireHour() {
        return expireHour;
    }

    public GlueConfigDTO setExpireHour(Double expireHour) {
        this.expireHour = expireHour;
        return this;
    }

    public Double getRewarmingHour() {
        return rewarmingHour;
    }

    public GlueConfigDTO setRewarmingHour(Double rewarmingHour) {
        this.rewarmingHour = rewarmingHour;
        return this;
    }

    public JSONArray getRate() {
        return rate;
    }

    public GlueConfigDTO setRate(JSONArray rate) {
        this.rate = rate;
        return this;
    }

    public String getNote() {
        return note;
    }

    public GlueConfigDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public Double getStorageExpireHour() {
        return storageExpireHour;
    }

    public GlueConfigDTO setStorageExpireHour(Double storageExpireHour) {
        this.storageExpireHour = storageExpireHour;
        return this;
    }

    public Double getDeaerationHour() {
        return deaerationHour;
    }

    public GlueConfigDTO setDeaerationHour(Double deaerationHour) {
        this.deaerationHour = deaerationHour;
        return this;
    }

    public Double getQualityExpireHour() {
        return qualityExpireHour;
    }

    public GlueConfigDTO setQualityExpireHour(Double qualityExpireHour) {
        this.qualityExpireHour = qualityExpireHour;
        return this;
    }

    public Double getRecoveryHour() {
        return recoveryHour;
    }

    public GlueConfigDTO setRecoveryHour(Double recoveryHour) {
        this.recoveryHour = recoveryHour;
        return this;
    }

    public Integer getMaxRewarmingTimes() {
        return maxRewarmingTimes;
    }

    public GlueConfigDTO setMaxRewarmingTimes(Integer maxRewarmingTimes) {
        this.maxRewarmingTimes = maxRewarmingTimes;
        return this;
    }

    public Integer getComponentNumber() {
        return componentNumber;
    }

    public GlueConfigDTO setComponentNumber(Integer componentNumber) {
        this.componentNumber = componentNumber;
        return this;
    }

    public GlueWorkFlowDTO getGlueWorkFlow() {
        return glueWorkFlow;
    }

    public GlueConfigDTO setGlueWorkFlow(GlueWorkFlowDTO glueWorkFlow) {
        this.glueWorkFlow = glueWorkFlow;
        return this;
    }
}
