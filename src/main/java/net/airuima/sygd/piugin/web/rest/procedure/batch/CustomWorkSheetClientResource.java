package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import net.airuima.domain.base.process.WorkFlowConvertConfig;
import net.airuima.dto.procddure.single.SnWorkStatusDTO;
import net.airuima.service.client.ClientSnService;
import net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient;
import net.airuima.sygd.piugin.service.procedure.CustomWorkSheetClientService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.SnWorkStatusQueryDTO;
import net.airuima.util.HeaderUtil;
import net.airuima.util.ResponseData;
import net.airuima.util.ResponseException;
import net.airuima.web.ProtectBaseResource;
import net.airuima.web.rest.errors.BadRequestAlertException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单客户Resource
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Tag(name = "工单客户Resource")
@RestController
@RequestMapping("/plugins/api/work-sheet-clients")
public class CustomWorkSheetClientResource extends ProtectBaseResource<CustomWorkSheetClient> {

    private final CustomWorkSheetClientService customWorkSheetClientService;

    @Autowired
    private ClientSnService clientSnService;

    public CustomWorkSheetClientResource(CustomWorkSheetClientService customWorkSheetClientService) {
        this.customWorkSheetClientService = customWorkSheetClientService;
    }

    /**
     * 重写新增函数
     *
     * @param entity 工单客户
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 工单客户
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<CustomWorkSheetClient> create(@Valid @RequestBody CustomWorkSheetClient entity) throws URISyntaxException {
        try {
            customWorkSheetClientService.saveWorkSheetClient(entity);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 重写新增函数
     *
     * @param entityList 工单客户
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 工单客户
     */

    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_CREATE')) or hasAnyAuthority('ROLE_ADMIN')")
    @PostMapping("/batch")
    public ResponseEntity<CustomWorkSheetClient> batchCreate(@Valid @RequestBody List<CustomWorkSheetClient> entityList) throws URISyntaxException {
        try {
            customWorkSheetClientService.batchSaveWorkSheetClient(entityList);
            return ResponseEntity.ok().headers(HeaderUtil.createdAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 重写更新函数，
     *
     * @param entity 工单客户
     * @return org.springframework.http.ResponseEntity<net.airuima.domain.base.process.CustomWorkSheetClient> 工单客户
     */
    @Override
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_UPDATE')) or hasAnyAuthority('ROLE_ADMIN')")
    public ResponseEntity<CustomWorkSheetClient> update(@Valid @RequestBody CustomWorkSheetClient entity) throws URISyntaxException {
        try {
            customWorkSheetClientService.saveWorkSheetClient(entity);
            return ResponseEntity.ok().headers(HeaderUtil.updatedAlert(this.entityName, "")).build();
        } catch (BadRequestAlertException e) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, e.getErrorKey(), e.getTitle())).build();
        } catch (ResponseException responseException) {
            return ResponseEntity.badRequest().headers(HeaderUtil.alertWithMessage(responseException.getErrorKey(), responseException.getMessage())).build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(this.entityName, "exception", e.getMessage())).build();
        }
    }

    /**
     * 删除工单客户客户
     *
     * @param id 主键
     * @return org.springframework.http.ResponseEntity<Void>  响应
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/{id}"})
    @Override
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        customWorkSheetClientService.deleteEntity(Arrays.asList(id));
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(CustomWorkSheetClient.class.getSimpleName()), "")).build();
    }

    /**
     * 删除工单客户
     *
     * @param ids 主键
     * @return org.springframework.http.ResponseEntity<Void>  响应
     */
    @PreAuthorize("hasAnyAuthority(#root.this.getRequiredAuthority('ENTITY_DELETE')) or hasAnyAuthority('ROLE_ADMIN')")
    @DeleteMapping({"/batch/delete"})
    public ResponseEntity<Void> batchDelete(@RequestBody List<Long> ids) {
        customWorkSheetClientService.deleteEntity(ids);
        return ResponseEntity.ok().headers(HeaderUtil.deletedAlert(StringUtils.uncapitalize(CustomWorkSheetClient.class.getSimpleName()), "")).build();
    }

    /**
     * 根据工单编码查询工单客户
     *
     * @param serialNumber 工单编码
     * @return org.springframework.http.ResponseEntity<net.airuima.sygd.piugin.domain.procedure.batch.CustomWorkSheetClient> 工单客户
     */
    @Operation(summary = "根据工单编码查询工单客户")
    @GetMapping("/worksheet")
    public ResponseEntity<ResponseData<List<CustomWorkSheetClient>>> findByWorkSheet(@RequestParam(name = "serialNumber") String serialNumber) {
        return ResponseData.ok(customWorkSheetClientService.findByWorkSheet(serialNumber));
    }


    /**
     * 获取当前sn 最新状态信息
     *
     * @param snWorkStatusQueryDTO 查询sn状态参数
     * @return SnWorkStatusDTO
     * <AUTHOR>
     * @date 2022/10/17
     */
    @GetMapping("/sn/status")
    public ResponseEntity<ResponseData<SnWorkStatusDTO>> currentSnInfo(@RequestBody SnWorkStatusQueryDTO snWorkStatusQueryDTO) {
        try {
            String sn = snWorkStatusQueryDTO.getSn();
            SnWorkStatusDTO snWorkStatusDto = clientSnService.getSnWorkStatusInfo(sn);
            return ResponseData.ok((snWorkStatusDto));
        } catch (ResponseException responseException) {
            return ResponseData.error(responseException);
        } catch (Exception e) {
            return ResponseData.error(e);
        }
    }

}
