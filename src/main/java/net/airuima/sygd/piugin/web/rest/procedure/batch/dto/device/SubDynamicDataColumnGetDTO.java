package net.airuima.sygd.piugin.web.rest.procedure.batch.dto.device;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import net.airuima.dto.document.DocumentDTO;

import java.io.Serializable;
import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 动态元数据定义表Domain
 *
 * <AUTHOR>
 * @date 2022-08-25
 */
@Schema(description = "获取子动态元数据DTO")
public class SubDynamicDataColumnGetDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;

    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 表单展示顺序
     */
    @Schema(description = "表单展示顺序")
    private int formOrder;

    /**
     * 是否启用
     */
    @Schema(description = "是否启用")
    private Boolean isEnable;

    /**
     * 动态元数据值
     */
    @Schema(description = "动态元数据值")
    private String columnValue;

    /**
     * 判定结果(true:合格;false)
     *  数值类型的若在区间范围内则合格，反之不合格
     *  对于字符串类型、文件类型一律认为合格
     *  对于布尔值类型的则取用户的选项
     */
    @Schema(description = "判定结果(true:合格;false)(数值类型的若在区间范围内则合格，反之不合格,字符串、文件一律合格)")
    private boolean result = Boolean.TRUE;

    /**
     * 文件集合
     */
    @Schema(description = "文件集合")
    private List<DocumentDTO> documentDTOList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getFormOrder() {
        return formOrder;
    }

    public void setFormOrder(int formOrder) {
        this.formOrder = formOrder;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public SubDynamicDataColumnGetDTO setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
        return this;
    }

    public String getColumnValue() {
        return columnValue;
    }

    public SubDynamicDataColumnGetDTO setColumnValue(String columnValue) {
        this.columnValue = columnValue;
        return this;
    }

    public boolean getResult() {
        return result;
    }

    public SubDynamicDataColumnGetDTO setResult(boolean result) {
        this.result = result;
        return this;
    }

    public List<DocumentDTO> getDocumentDTOList() {
        return documentDTOList;
    }

    public SubDynamicDataColumnGetDTO setDocumentDTOList(List<DocumentDTO> documentDTOList) {
        this.documentDTOList = documentDTOList;
        return this;
    }
}
