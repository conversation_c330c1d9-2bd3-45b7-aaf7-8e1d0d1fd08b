package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 转容器响应
 */
@Schema(description = "转容器响应")
public class ContainerRemainDTO {

    /**
     * 记录id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "记录id")
    private Long id;

    /**
     * 容器号
     */
    @Schema(description = "容器号")
    private String containerCode;

    /**
     * 子工单号
     */
    @Schema(description = "子工单号")
    private String subWorkSheetSerialNumber;

    /**
     * 工单号
     */
    @Schema(description = "工单号")
    private String workSheetSerialNumber;

    /**
     * 工位名称
     */
    @Schema(description = "工位名称")
    private String workCellName;

    /**
     * 工位编码
     */
    @Schema(description = "工位编码")
    private String workCellCode;


    /**
     * 工序名称
     */
    @Schema(description = "工序名称")
    private String stepName;

    /**
     * 工序编码
     */
    @Schema(description = "工序编码")
    private String stepCode;

    /**
     * 数量
     */
    @Schema(description = "数量")
    private Integer number;

    /**
     * 剩余数量
     */
    @Schema(description = "剩余数量")
    private Integer remainNumber;

    /**
     * 员工姓名
     */
    @Schema(description = "员工姓名")
    private String staffName;

    /**
     * 员工编码
     */
    @Schema(description = "员工编码")
    private String staffCode;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private String createdDate;

    public String getCreatedDate() {
        return createdDate;
    }

    public ContainerRemainDTO setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
        return this;
    }

    public Long getId() {
        return id;
    }

    public ContainerRemainDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public String getStepName() {
        return stepName;
    }

    public ContainerRemainDTO setStepName(String stepName) {
        this.stepName = stepName;
        return this;
    }

    public String getStepCode() {
        return stepCode;
    }

    public ContainerRemainDTO setStepCode(String stepCode) {
        this.stepCode = stepCode;
        return this;
    }

    public String getContainerCode() {
        return containerCode;
    }

    public ContainerRemainDTO setContainerCode(String containerCode) {
        this.containerCode = containerCode;
        return this;
    }

    public String getSubWorkSheetSerialNumber() {
        return subWorkSheetSerialNumber;
    }

    public ContainerRemainDTO setSubWorkSheetSerialNumber(String subWorkSheetSerialNumber) {
        this.subWorkSheetSerialNumber = subWorkSheetSerialNumber;
        return this;
    }

    public String getWorkSheetSerialNumber() {
        return workSheetSerialNumber;
    }

    public ContainerRemainDTO setWorkSheetSerialNumber(String workSheetSerialNumber) {
        this.workSheetSerialNumber = workSheetSerialNumber;
        return this;
    }

    public String getWorkCellName() {
        return workCellName;
    }

    public ContainerRemainDTO setWorkCellName(String workCellName) {
        this.workCellName = workCellName;
        return this;
    }

    public String getWorkCellCode() {
        return workCellCode;
    }

    public ContainerRemainDTO setWorkCellCode(String workCellCode) {
        this.workCellCode = workCellCode;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ContainerRemainDTO setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getRemainNumber() {
        return remainNumber;
    }

    public ContainerRemainDTO setRemainNumber(Integer remainNumber) {
        this.remainNumber = remainNumber;
        return this;
    }

    public String getStaffName() {
        return staffName;
    }

    public ContainerRemainDTO setStaffName(String staffName) {
        this.staffName = staffName;
        return this;
    }

    public String getStaffCode() {
        return staffCode;
    }

    public ContainerRemainDTO setStaffCode(String staffCode) {
        this.staffCode = staffCode;
        return this;
    }
}
