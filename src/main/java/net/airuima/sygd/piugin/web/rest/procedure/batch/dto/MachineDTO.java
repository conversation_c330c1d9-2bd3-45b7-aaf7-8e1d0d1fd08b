package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 设备
 *
 * <AUTHOR>
 * @date 2025-01-03
 */
@Schema(description = "设备")
public class MachineDTO {

    @Schema(description = "id")
    private Long id;
    /**
     * 名称
     */
    @Schema(description = "名称")
    private String name;


    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    public MachineDTO() {
    }

    public MachineDTO(Long id, String name, String code) {
        this.id = id;
        this.name = name;
        this.code = code;
    }

    public Long getId() {
        return id;
    }

    public MachineDTO setId(Long id) {
        this.id = id;
        return this;
    }

    public MachineDTO(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public MachineDTO setName(String name) {
        this.name = name;
        return this;
    }

    public String getCode() {
        return code;
    }

    public MachineDTO setCode(String code) {
        this.code = code;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MachineDTO that = (MachineDTO) o;
        return Objects.equals(name, that.name) && Objects.equals(code, that.code);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, code);
    }
}
