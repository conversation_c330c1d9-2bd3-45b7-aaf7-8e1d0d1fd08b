package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * MES数据请求DTO
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Schema(description = "MES数据请求DTO")
public class MesDataRequestDTO {
    /**
     * 编码
     */
    @Schema(description = "编码")
    private String code;

    /**
     * 类别
     */
    @Schema(description = "类别 0子工单 1工序 2员工 3工位 4容器 5物料 6产品谱系 7设备 8 易损件 ")
    private Integer category;

    public String getCode() {
        return code;
    }

    public MesDataRequestDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public MesDataRequestDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }
}
