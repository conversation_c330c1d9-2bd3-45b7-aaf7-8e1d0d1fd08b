package net.airuima.sygd.piugin.web.rest.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.servlet.http.HttpServletResponse;
import net.airuima.sygd.piugin.service.procedure.WorkFlowOrderService;
import net.airuima.sygd.piugin.web.rest.procedure.batch.dto.WorkFlowOrderDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 生产工艺流程单Resource
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Schema(name = "生产工艺流程单")
@RestController
@RequestMapping("/plugins/api/work-flow-orders")
public class WorkFlowOrderResource {

    private final WorkFlowOrderService workFlowOrderService;

    public WorkFlowOrderResource(WorkFlowOrderService workFlowOrderService) {
        this.workFlowOrderService = workFlowOrderService;
    }

    @PostMapping({"/export"})
    public void export(@RequestBody WorkFlowOrderDTO workFlowOrderDTO, HttpServletResponse response) throws Exception {
        workFlowOrderService.export(workFlowOrderDTO, response);
    }
}
