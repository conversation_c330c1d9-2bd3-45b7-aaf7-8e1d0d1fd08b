package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水流程DTO
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Schema(description = "胶水流程DTO")
public class GlueWorkFlowDTO {

    /**
     * 胶水流程编码
     */
    @Schema(description = "胶水流程编码")
    private String code;
    /**
     * 胶水流程名称
     */
    @Schema(description = "胶水流程名称")
    private String name;
    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private Boolean isEnable;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    public Boolean getEnable() {
        return isEnable;
    }

    public GlueWorkFlowDTO setEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }

    /**
     * 步骤信息
     */
    @Schema(description = "步骤信息")
    private List<GlueStepDTO> stepInfos;

    public String getCode() {
        return code;
    }

    public GlueWorkFlowDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public GlueWorkFlowDTO setName(String name) {
        this.name = name;
        return this;
    }




    public String getNote() {
        return note;
    }

    public GlueWorkFlowDTO setNote(String note) {
        this.note = note;
        return this;
    }

    public List<GlueStepDTO> getStepInfos() {
        return stepInfos;
    }

    public GlueWorkFlowDTO setStepInfos(List<GlueStepDTO> stepInfos) {
        this.stepInfos = stepInfos;
        return this;
    }
}
