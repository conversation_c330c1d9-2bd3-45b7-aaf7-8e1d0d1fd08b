package net.airuima.sygd.piugin.web.rest.procedure.batch.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 胶水步骤DTO
 *
 * <AUTHOR>
 * @date 2024-10-17
 */
@Schema(description = "胶水步骤DTO")
public class GlueStepDTO {

    /**
     * 步骤管理编码
     */
    @Schema(description = "步骤管理编码")
    private String code;
    /**
     * 步骤管理名称
     */
    @Schema(description = "步骤管理名称")
    private String name;
    /**
     * 步骤类型：0存储、1调制、2领用、3回收、4报废
     */
    @Schema(description = "步骤类型：0存储、1调制、2领用、3回收、4报废")
    private Integer category;

    /**
     * 权限id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @Schema(description = "权限id")
    private Long authorityId;


    /**
     * 是否启用(0:禁用;1:启用)
     */
    @Schema(description = "是否启用(0:禁用;1:启用)")
    private Boolean isEnable;
    /**
     * 备注
     */
    @Schema(description = "备注")
    private String note;


    public String getCode() {
        return code;
    }

    public GlueStepDTO setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public GlueStepDTO setName(String name) {
        this.name = name;
        return this;
    }

    public Integer getCategory() {
        return category;
    }

    public GlueStepDTO setCategory(Integer category) {
        this.category = category;
        return this;
    }

    public Long getAuthorityId() {
        return authorityId;
    }

    public GlueStepDTO setAuthorityId(Long authorityId) {
        this.authorityId = authorityId;
        return this;
    }

    public Boolean getEnable() {
        return isEnable;
    }

    public GlueStepDTO setEnable(Boolean enable) {
        isEnable = enable;
        return this;
    }

    public String getNote() {
        return note;
    }

    public GlueStepDTO setNote(String note) {
        this.note = note;
        return this;
    }
}
