package net.airuima.sygd.piugin.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单客户
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Entity
@Table(name = "plugin_procedure_worksheet_client")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class CustomWorkSheetClient extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;


    /**
     * 客户编码
     */
    @Schema(description = "客户编码")
    @Column(name = "code")
    private String code;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @Column(name = "name")
    private String name;

    /**
     * 客户全称
     */
    @Schema(description = "客户全称")
    @Column(name = "full_name")
    private String fullName;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public CustomWorkSheetClient setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public String getCode() {
        return code;
    }

    public CustomWorkSheetClient setCode(String code) {
        this.code = code;
        return this;
    }

    public String getName() {
        return name;
    }

    public CustomWorkSheetClient setName(String name) {
        this.name = name;
        return this;
    }

    public String getFullName() {
        return fullName;
    }

    public CustomWorkSheetClient setFullName(String fullName) {
        this.fullName = fullName;
        return this;
    }
}
