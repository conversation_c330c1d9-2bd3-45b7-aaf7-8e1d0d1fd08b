package net.airuima.sygd.piugin.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

@Entity
@Table(name = "plugin_procedure_change_container_info")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "changeContainerInfoEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"), @NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"), @NamedAttributeNode("workLine")})})
public class ChangeContainerInfo extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;


    /**
     * 合并的容器
     */
    @ManyToOne
    @Schema(description = "合并的容器")
    @JoinColumn(name = "merge_container_id")
    private Container mergeContainer;

    /**
     * /**
     * 大数量
     */
    @Schema(description = "大数量")
    @Column(name = "big_number")
    private Integer bigNumber;


    /**
     * 小数量
     */
    @Schema(description = "小数量")
    @Column(name = "smallNumber")
    private Integer smallNumber;

    /**
     * 剩余数量
     */
    @Schema(description = "剩余数量")
    @Column(name = "remain_number")
    private Integer remainNumber;

    /**
     * 最后容器的数量
     */
    @Schema(description = "最后容器的数量")
    @Column(name = "last_number")
    private Integer lastNumber;

    public Integer getLastNumber() {
        return lastNumber;
    }

    public ChangeContainerInfo setLastNumber(Integer lastNumber) {
        this.lastNumber = lastNumber;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public ChangeContainerInfo setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public ChangeContainerInfo setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public Container getMergeContainer() {
        return mergeContainer;
    }

    public ChangeContainerInfo setMergeContainer(Container mergeContainer) {
        this.mergeContainer = mergeContainer;
        return this;
    }

    public Integer getBigNumber() {
        return bigNumber;
    }

    public ChangeContainerInfo setBigNumber(Integer bigNumber) {
        this.bigNumber = bigNumber;
        return this;
    }

    public Integer getSmallNumber() {
        return smallNumber;
    }

    public ChangeContainerInfo setSmallNumber(Integer smallNumber) {
        this.smallNumber = smallNumber;
        return this;
    }

    public Integer getRemainNumber() {
        return remainNumber;
    }

    public ChangeContainerInfo setRemainNumber(Integer remainNumber) {
        this.remainNumber = remainNumber;
        return this;
    }
}
