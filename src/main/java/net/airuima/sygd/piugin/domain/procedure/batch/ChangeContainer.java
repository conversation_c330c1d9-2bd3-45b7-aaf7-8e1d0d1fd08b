package net.airuima.sygd.piugin.domain.procedure.batch;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import net.airuima.config.annotation.FetchDataFilter;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.base.process.Step;
import net.airuima.domain.base.scene.WorkCell;
import net.airuima.domain.procedure.aps.SubWorkSheet;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.domain.procedure.batch.Container;
import net.airuima.dto.organization.StaffDTO;
import net.airuima.query.annotation.FetchEntity;
import net.airuima.query.annotation.FetchField;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;
import java.util.Objects;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单容器关联Domain
 *
 * <AUTHOR>
 * @date 2024-08-20
 */
@Entity
@Table(name = "plugin_procedure_change_container")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
@NamedEntityGraph(name = "changeContainerEntityGraph", attributeNodes = {
        @NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"),
        @NamedAttributeNode(value = "subWorkSheet", subgraph = "subWorkSheetEntityGraph")},
        subgraphs = {
                @NamedSubgraph(name = "subWorkSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode(value = "workSheet", subgraph = "workSheetEntityGraph"), @NamedAttributeNode("workLine")}),
                @NamedSubgraph(name = "workSheetEntityGraph",
                        attributeNodes = {@NamedAttributeNode("pedigree"), @NamedAttributeNode("workLine")})})
public class ChangeContainer extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 子工单
     */
    @ManyToOne
    @Schema(description = "子工单")
    @JoinColumn(name = "sub_work_sheet_id")
    private SubWorkSheet subWorkSheet;

    /**
     * 工位ID
     */
    @Schema(description = "工位ID")
    @ManyToOne
    @JoinColumn(name = "work_cell_id")
    private WorkCell workCell;


    /**
     * 容器
     */
    @ManyToOne
    @Schema(description = "容器")
    @JoinColumn(name = "container_id")
    private Container container;

    /**
     * 原来的容器
     */
    @ManyToOne
    @Schema(description = "原来的容器")
    @JoinColumn(name = "origin_container_id")
    private Container originContainer;

    /**
     * 工序
     */
    @ManyToOne
    @Schema(description = "工序")
    @JoinColumn(name = "step_id")
    private Step step;

    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "number")
    private Integer number;


    /**
     * 数量
     */
    @Schema(description = "数量")
    @Column(name = "remain_number")
    private Integer remainNumber;

    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Column(name = "operator_id")
    private Long operatorId;

    /**
     * 操作人DTO
     */
    @Schema(description = "操作人DTO")
    @FetchField(mapUri = "/api/staff", serviceId = "organization", paramKey = "operatorId")
    @FetchDataFilter(schema = "organization",tableName = "staff",foreignKey = "operator_id")
    @Transient
    private StaffDTO operatorDto = new StaffDTO();


    /**
     * 版本
     */
    @Schema(description = "版本")
    @Column(name = "version")
    private Integer version;

    public Integer getVersion() {
        return version;
    }

    public ChangeContainer setVersion(Integer version) {
        this.version = version;
        return this;
    }

    public Step getStep() {
        return step;
    }

    public ChangeContainer setStep(Step step) {
        this.step = step;
        return this;
    }

    public Container getContainer() {
        return container;
    }

    public ChangeContainer setContainer(Container container) {
        this.container = container;
        return this;
    }

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public ChangeContainer setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public SubWorkSheet getSubWorkSheet() {
        return subWorkSheet;
    }

    public ChangeContainer setSubWorkSheet(SubWorkSheet subWorkSheet) {
        this.subWorkSheet = subWorkSheet;
        return this;
    }

    public WorkCell getWorkCell() {
        return workCell;
    }

    public ChangeContainer setWorkCell(WorkCell workCell) {
        this.workCell = workCell;
        return this;
    }

    public Container getOriginContainer() {
        return originContainer;
    }

    public ChangeContainer setOriginContainer(Container originContainer) {
        this.originContainer = originContainer;
        return this;
    }

    public Integer getNumber() {
        return number;
    }

    public ChangeContainer setNumber(Integer number) {
        this.number = number;
        return this;
    }

    public Integer getRemainNumber() {
        return remainNumber;
    }

    public ChangeContainer setRemainNumber(Integer remainNumber) {
        this.remainNumber = remainNumber;
        return this;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public ChangeContainer setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
        return this;
    }

    public StaffDTO getOperatorDto() {
        return operatorDto;
    }

    public ChangeContainer setOperatorDto(StaffDTO operatorDto) {
        this.operatorDto = operatorDto;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChangeContainer changeContainer = (ChangeContainer) o;
        if (changeContainer.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), changeContainer.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }
}
