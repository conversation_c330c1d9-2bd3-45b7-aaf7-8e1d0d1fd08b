package net.airuima.sygd.piugin.domain.procedure.batch;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import net.airuima.domain.base.CustomBaseEntity;
import net.airuima.domain.procedure.aps.WorkSheet;
import net.airuima.query.annotation.FetchEntity;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.io.Serializable;

/**
 * Copyright (C), 2017-2020, 武汉睿码智能科技有限公司
 * 工单订单信息
 *
 * <AUTHOR>
 * @date 2025-05-10
 */
@Entity
@Table(name = "plugin_procedure_worksheet_client_order")
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE)
@DynamicInsert
@DynamicUpdate
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorColumn
@DiscriminatorValue(value = "base")
@FetchEntity
public class CustomWorkSheetClientOrder extends CustomBaseEntity implements Serializable {

    /**
     * 工单
     */
    @ManyToOne
    @Schema(description = "工单")
    @JoinColumn(name = "work_sheet_id")
    private WorkSheet workSheet;

    /**
     * 类别
     */
    @Schema(description = "类别")
    @Column(name = "type")
    private String type;

    /**
     * 单号
     */
    @Schema(description = "单号")
    @Column(name = "serial_number")
    private String serialNumber;

    /**
     * 序号
     */
    @Schema(description = "序号")
    @Column(name = "number")
    private String number;


    /**
     * 品号
     */
    @Schema(description = "品号")
    @Column(name = "pedigree")
    private String pedigree;


    /**
     * 品名
     */
    @Schema(description = "品名")
    @Column(name = "pedigree_name")
    private String pedigreeName;


    /**
     * 规格
     */
    @Schema(description = "规格")
    @Column(name = "specification")
    private String specification;

    /**
     * 订单数量
     */
    @Schema(description = "订单数量")
    @Column(name = "order_number")
    private Integer orderNumber;

    /**
     * 已交数量
     */
    @Schema(description = "已交数量")
    @Column(name = "submitted_number")
    private Integer submittedNumber;

    /**
     * 未交数量
     */
    @Schema(description = "未交数量")
    @Column(name = "unsubmitted_number")
    private Integer unsubmittedNumber;

    /**
     * 客户编号
     */
    @Schema(description = "客户编号")
    @Column(name = "client_code")
    private String clientCode;


    /**
     * 客户名称
     */
    @Schema(description = "客户名称")
    @Column(name = "client_name")
    private String clientName;


    /**
     * 客户单号
     */
    @Schema(description = "客户单号")
    @Column(name = "client_order")
    private String clientOrder;


    /**
     * 加工单号
     */
    @Schema(description = "加工单号")
    @Column(name = "do_work_sheet")
    private String doWorkSheet;


    /**
     * 订单单头备注
     */
    @Schema(description = "订单单头备注")
    @Column(name = "order_head_note")
    private String orderHeadNote;


    /**
     * 订单单身备注
     */
    @Schema(description = "订单单身备注")
    @Column(name = "order_body_note")
    private String orderBodyNote;


    /**
     * 订单日期
     */
    @Schema(description = "订单日期")
    @Column(name = "order_date")
    private String orderDate;

    public WorkSheet getWorkSheet() {
        return workSheet;
    }

    public CustomWorkSheetClientOrder setWorkSheet(WorkSheet workSheet) {
        this.workSheet = workSheet;
        return this;
    }

    public String getType() {
        return type;
    }

    public CustomWorkSheetClientOrder setType(String type) {
        this.type = type;
        return this;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public CustomWorkSheetClientOrder setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
        return this;
    }

    public String getNumber() {
        return number;
    }

    public CustomWorkSheetClientOrder setNumber(String number) {
        this.number = number;
        return this;
    }

    public String getPedigree() {
        return pedigree;
    }

    public CustomWorkSheetClientOrder setPedigree(String pedigree) {
        this.pedigree = pedigree;
        return this;
    }

    public String getPedigreeName() {
        return pedigreeName;
    }

    public CustomWorkSheetClientOrder setPedigreeName(String pedigreeName) {
        this.pedigreeName = pedigreeName;
        return this;
    }

    public String getSpecification() {
        return specification;
    }

    public CustomWorkSheetClientOrder setSpecification(String specification) {
        this.specification = specification;
        return this;
    }

    public Integer getOrderNumber() {
        return orderNumber;
    }

    public CustomWorkSheetClientOrder setOrderNumber(Integer orderNumber) {
        this.orderNumber = orderNumber;
        return this;
    }

    public Integer getSubmittedNumber() {
        return submittedNumber;
    }

    public CustomWorkSheetClientOrder setSubmittedNumber(Integer submittedNumber) {
        this.submittedNumber = submittedNumber;
        return this;
    }

    public Integer getUnsubmittedNumber() {
        return unsubmittedNumber;
    }

    public CustomWorkSheetClientOrder setUnsubmittedNumber(Integer unsubmittedNumber) {
        this.unsubmittedNumber = unsubmittedNumber;
        return this;
    }

    public String getClientCode() {
        return clientCode;
    }

    public CustomWorkSheetClientOrder setClientCode(String clientCode) {
        this.clientCode = clientCode;
        return this;
    }

    public String getClientName() {
        return clientName;
    }

    public CustomWorkSheetClientOrder setClientName(String clientName) {
        this.clientName = clientName;
        return this;
    }

    public String getClientOrder() {
        return clientOrder;
    }

    public CustomWorkSheetClientOrder setClientOrder(String clientOrder) {
        this.clientOrder = clientOrder;
        return this;
    }

    public String getDoWorkSheet() {
        return doWorkSheet;
    }

    public CustomWorkSheetClientOrder setDoWorkSheet(String doWorkSheet) {
        this.doWorkSheet = doWorkSheet;
        return this;
    }

    public String getOrderHeadNote() {
        return orderHeadNote;
    }

    public CustomWorkSheetClientOrder setOrderHeadNote(String orderHeadNote) {
        this.orderHeadNote = orderHeadNote;
        return this;
    }

    public String getOrderBodyNote() {
        return orderBodyNote;
    }

    public CustomWorkSheetClientOrder setOrderBodyNote(String orderBodyNote) {
        this.orderBodyNote = orderBodyNote;
        return this;
    }

    public String getOrderDate() {
        return orderDate;
    }

    public CustomWorkSheetClientOrder setOrderDate(String orderDate) {
        this.orderDate = orderDate;
        return this;
    }
}
