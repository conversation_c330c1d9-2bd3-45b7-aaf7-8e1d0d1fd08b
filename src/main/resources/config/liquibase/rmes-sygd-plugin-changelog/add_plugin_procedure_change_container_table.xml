<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="simon" id="202409141116">
        <createTable tableName="plugin_procedure_change_container" remarks="工单容器转换表">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="bigint(20)"/>
            <column name="work_cell_id" remarks="工位id" type="bigint(20)">
                <constraints nullable="false"/>
            </column>
            <column name="origin_container_id" remarks="原来容器id" type="bigint(20)">
                <constraints nullable="false"/>
            </column>
            <column name="container_id" remarks="容器id" type="bigint(20)"/>
            <column name="operator_id" remarks="操作人" type="bigint(20)"/>
            <column defaultValueNumeric="0" name="number" remarks="数量" type="int(11)" />
            <column defaultValueNumeric="0" name="remain_number" remarks="剩余数量" type="int(11)" />
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_work_sheet_index">
            <column name="work_sheet_id"/>
        </createIndex>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_sub_work_sheet_index">
            <column name="sub_work_sheet_id"/>
        </createIndex>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_origin_container_id_index">
            <column name="origin_container_id"/>
        </createIndex>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_container_id_index">
            <column name="container_id"/>
        </createIndex>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_work_cell_id_index">
            <column name="work_cell_id"/>
        </createIndex>
    </changeSet>

    <changeSet author="simon" id="202409181116">
        <addColumn tableName="plugin_procedure_change_container">
            <column name="step_id" type="BIGINT(20)" remarks="工序id"/>
        </addColumn>
        <createIndex tableName="plugin_procedure_change_container" indexName="plugin_procedure_change_container_step_id_index">
            <column name="step_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
