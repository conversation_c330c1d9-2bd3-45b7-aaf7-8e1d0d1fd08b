<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">


    <changeSet author="simon" id="202505131116">
        <createTable tableName="plugin_procedure_worksheet_client_order" remarks="工单订单">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="type" remarks="类别" type="varchar(255)"/>
            <column name="serial_number" remarks="单号" type="varchar(255)"/>
            <column name="number" remarks="序号" type="varchar(255)"/>
            <column name="pedigree" remarks="品号" type="varchar(255)"/>
            <column name="pedigree_name" remarks="品名" type="varchar(255)"/>
            <column name="specification" remarks="规格" type="varchar(255)"/>
            <column name="order_number" remarks="订单数量" type="int"/>
            <column name="submitted_number" remarks="已交数量" type="int"/>
            <column name="unsubmitted_number" remarks="未交数量" type="int"/>
            <column name="client_code" remarks="客户编号" type="varchar(255)"/>
            <column name="client_name" remarks="客户名称" type="varchar(255)"/>
            <column name="client_order" remarks="客户单号" type="varchar(255)"/>
            <column name="do_work_sheet" remarks="加工单号" type="varchar(255)"/>
            <column name="order_head_note" remarks="订单单头备注" type="varchar(255)"/>
            <column name="order_body_note" remarks="订单单身备注" type="varchar(255)"/>
            <column name="order_date" remarks="订单日期" type="varchar(255)"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
