<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd">

    <changeSet author="simon" id="202409141116">
        <createTable tableName="plugin_procedure_change_container_info" remarks="工单容器转换信息">
            <column autoIncrement="true" name="id" type="bigint">
                <constraints primaryKey="true"/>
            </column>
            <column name="work_sheet_id" remarks="工单ID" type="bigint(20)">
                <constraints nullable="true"/>
            </column>
            <column name="sub_work_sheet_id" remarks="子工单ID" type="bigint(20)"/>
            <column defaultValueNumeric="0" name="big_number" remarks="大数量" type="int(11)" />
            <column defaultValueNumeric="0" name="small_number" remarks="小数量" type="int(11)" />
            <column defaultValueNumeric="0" name="remain_number" remarks="剩余数量" type="int(11)" />
            <column defaultValueNumeric="0" name="last_number" remarks="最后一个容器的数量" type="int(11)" />
            <column name="merge_container_id" remarks="合并容器id" type="bigint(20)"/>
            <column defaultValueNumeric="0" name="deleted" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" remarks="新建人" type="varchar(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" remarks="新建时间" type="timestamp"/>
            <column name="last_modified_by" remarks="最新修改人" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="last_modified_date" remarks="最新修改时间" type="timestamp"/>
            <column name="custom1" remarks="定制字段" type="varchar(255)"/>
            <column name="custom2" remarks="定制字段" type="varchar(255)"/>
            <column name="custom3" remarks="定制字段" type="varchar(255)"/>
            <column name="custom4" remarks="定制字段" type="varchar(255)"/>
            <column name="custom5" remarks="定制字段" type="varchar(255)"/>
            <column name="dtype" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
